---
description: Refactor code following best practices and design patterns
---

## Your task

Refactor the following code: @$ARGUMENTS

Guidelines:
1. **Maintain functionality**: Ensure no breaking changes
2. **Improve readability**: Make code more self-documenting
3. **Extract common patterns**: Identify and extract reusable components
4. **Performance optimization**: Improve efficiency where possible
5. **Modern conventions**: Use current best practices
6. **Type safety**: Add or improve type annotations if applicable

Explain each change and why it's beneficial.