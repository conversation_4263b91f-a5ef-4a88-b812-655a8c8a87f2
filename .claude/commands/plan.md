Read gh issue ###
Make a detailed plan to accomplish this. Think hardest. How will we implement only the functionality we need right now?
Identify files that need to be changed
Do not include plans for legacy fallback unless required or explicitly requested.
Write a short overview of what you are about to do.
Write function names and 1-3 sentences about what the functions will do
Write test names and 5-10 words about what behavior each test should cover