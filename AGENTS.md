# Repository Guidelines

## Project Structure & Module Organization
- `src/gedsys2_authentication/`: Primary Python package (auth domain, APIs, models).
- `tests/`: Pytest suite mirroring `src/` (e.g., `tests/api/test_login.py`).
- `scripts/`: Utility CLI scripts for local/dev tasks.
- `migrations/` or `alembic/`: DB migrations if applicable.
- `docker/` and `.github/`: Container and CI assets.

## Build, Test, and Development Commands
- `make init`: Install dependencies and set up pre-commit.
- `make lint`: Run formatters/linters (black, isort, flake8, mypy if enabled).
- `make test`: Run the test suite (pytest) with coverage.
- `make run`: Start the local app/service (e.g., uvicorn or Django dev server).
Examples (without Make): `pip install -r requirements.txt && pytest -q`, or with Poetry: `poetry install && poetry run pytest`.

## Coding Style & Naming Conventions
- Python 3.11+, 4-space indentation, UTF-8, Unix line endings.
- Formatting: black (line-length 88) + isort (profile=black).
- Linting: flake8 (style) + mypy (typing) when available.
- Naming: modules/functions `snake_case`, classes `PascalCase`, constants/env `UPPER_SNAKE_CASE`.
- Prefer type hints; annotate public interfaces and dataclasses/models.

## Testing Guidelines
- Framework: pytest. Place tests under `tests/` mirroring `src/` paths.
- Naming: files `test_*.py`, tests `test_*` functions or `Test*` classes.
- Coverage: aim ≥ 85% on changed code. Run: `pytest --maxfail=1 --ff --cov=src`.
- Use factories/fixtures; avoid network/DB in unit tests unless marked.

## Commit & Pull Request Guidelines
- Commits: Conventional Commits — e.g., `feat: add JWT refresh`, `fix(api): 401 on expired token`.
- Scope small, messages imperative, include rationale when non-obvious.
- PRs: clear description, link issues (`Closes #123`), include testing notes and screenshots for UI (if any).
- Checks must pass (lint, tests, type-check). Request review once green.

## Security & Configuration
- Configuration via environment variables; document new vars in `.env.example`.
- Never commit secrets; rotate credentials if exposed.
- Validate inputs, avoid logging sensitive data; consider `bandit -r src` for static checks.

