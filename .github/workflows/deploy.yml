name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: registry.gedsys.dev
  IMAGE_NAME: gedsys2/gedsys2-authentication
  REGISTRY_CACHE_REF: registry.gedsys.dev/gedsys2/gedsys2-authentication:buildcache
  CACHE_SCOPE: ${{ github.repository }}-${{ github.ref_name }}
  # Control de caché: usar sin caché para layers de aplicación en main branch
  NO_CACHE_APP_LAYERS: ${{ github.ref == 'refs/heads/main' }}

permissions:
  contents: read
  packages: write
  actions: read

jobs:
  build:
    runs-on: self-hosted
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run tests
      run: ./mvnw test

    - name: Build application
      run: ./mvnw clean package -DskipTests

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: jar-artifact
        path: target/*.jar

  docker:
    runs-on: self-hosted
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: jar-artifact
        path: target/

    - name: Debug - List files before Docker build
      run: |
        echo "Working directory contents:"
        ls -la
        echo "Target directory contents:"
        ls -la target/ || echo "target directory not found"
        echo "JAR files:"
        find . -name "*.jar" || echo "No JAR files found"

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        install: true
        driver-opts: image=moby/buildkit:latest

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.REGISTRY_USERNAME }}
        password: ${{ secrets.REGISTRY_PASSWORD }}

    - name: Probe GHA Cache availability
      id: cache_probe
      continue-on-error: true
      env:
        GH_REPO: ${{ github.repository }}
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        set -euo pipefail
        code=$(curl -sS -o /dev/null -w "%{http_code}" \
          -H "Authorization: Bearer $GH_TOKEN" \
          -H "Accept: application/vnd.github+json" \
          "https://api.github.com/repos/$GH_REPO/actions/caches?per_page=1")
        if [ "$code" = "200" ]; then
          echo "gha_cache_available=true" >> "$GITHUB_OUTPUT"
        else
          echo "gha_cache_available=false" >> "$GITHUB_OUTPUT"
        fi

    - name: Prepare local cache dirs
      run: |
        mkdir -p /tmp/.buildx-cache /tmp/.buildx-cache-new

    - name: Cache Strategy Info
      run: |
        echo "🔧 Cache Strategy Configuration:"
        echo "Branch: ${{ github.ref }}"
        echo "No cache for app layers: ${{ env.NO_CACHE_APP_LAYERS }}"
        if [ "${{ env.NO_CACHE_APP_LAYERS }}" = "true" ]; then
          echo "⚠️  Building without cache for application layers (main branch)"
          echo "✅ Base image layers will still use cache for efficiency"
        else
          echo "✅ Using full cache for development build"
        fi

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push (with GHA cache if available)
      id: build_with_gha
      continue-on-error: true
      if: steps.cache_probe.outputs.gha_cache_available == 'true'
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        no-cache: ${{ env.NO_CACHE_APP_LAYERS == 'true' }}
        cache-from: |
          type=gha,scope=${{ env.CACHE_SCOPE }}
          type=registry,ref=${{ env.REGISTRY_CACHE_REF }}
          type=local,src=/tmp/.buildx-cache
        cache-to: |
          type=gha,mode=max,scope=${{ env.CACHE_SCOPE }},ignore-error=true
          type=registry,ref=${{ env.REGISTRY_CACHE_REF }},mode=max,compression=zstd,oci-mediatypes=true
          type=local,dest=/tmp/.buildx-cache-new,mode=max

    - name: Build and push (fallback without GHA cache)
      id: build_no_gha
      if: steps.cache_probe.outputs.gha_cache_available != 'true' || steps.build_with_gha.outcome == 'failure'
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        no-cache: ${{ env.NO_CACHE_APP_LAYERS == 'true' }}
        cache-from: |
          type=registry,ref=${{ env.REGISTRY_CACHE_REF }}
          type=local,src=/tmp/.buildx-cache
        cache-to: |
          type=registry,ref=${{ env.REGISTRY_CACHE_REF }},mode=max,compression=zstd,oci-mediatypes=true
          type=local,dest=/tmp/.buildx-cache-new,mode=max

    - name: Rotate local cache
      if: always()
      run: |
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache || true

    - name: Verify image pushed
      if: always()
      env:
        TAGS: ${{ steps.meta.outputs.tags }}
      run: |
        set -euo pipefail
        FAIL=true
        # Convert multiline tags to array
        readarray -t tag_array <<< "$TAGS"
        for TAG in "${tag_array[@]}"; do
          if [ -n "$TAG" ]; then
            echo "Checking: $TAG"
            if docker buildx imagetools inspect "$TAG" >/dev/null 2>&1; then
              echo "✓ Found $TAG in registry"
              FAIL=false
              break
            else
              echo "⚠ Not found: $TAG"
            fi
          fi
        done
        if [ "$FAIL" = true ]; then
          echo "❌ Image not found in registry; failing."
          exit 1
        fi
        echo "✅ Push verified. Any cache errors will be ignored."

    - name: Build Summary
      if: always()
      run: |
        echo "## Docker Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Cache Strategy**: ${{ env.NO_CACHE_APP_LAYERS == 'true' && 'No cache for app layers (Production)' || 'Full cache (Development)' }}" >> $GITHUB_STEP_SUMMARY
        echo "- **GHA cache available**: ${{ steps.cache_probe.outputs.gha_cache_available }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Build with GHA outcome**: ${{ steps.build_with_gha.outcome }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Fallback used**: ${{ steps.cache_probe.outputs.gha_cache_available != 'true' || steps.build_with_gha.outcome == 'failure' }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Final result**: ✅ Images pushed successfully" >> $GITHUB_STEP_SUMMARY

  deploy:
    runs-on: self-hosted
    needs: docker
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Validate deployment configuration
      run: |
        if [ -z "${{ vars.INFRASTRUCTURE_DEPLOY_ENDPOINT }}" ]; then
          echo "❌ INFRASTRUCTURE_DEPLOY_ENDPOINT not configured"
          exit 1
        fi
        echo "✅ Deployment configuration validated"

    - name: Deploy to Infrastructure
      env:
        INFRASTRUCTURE_DEPLOY_ENDPOINT: ${{ vars.INFRASTRUCTURE_DEPLOY_ENDPOINT }}

      run: |
        echo "🚀 Triggering deployment to: $INFRASTRUCTURE_DEPLOY_ENDPOINT"
        
        response=$(curl -s -w "\n%{http_code}" --max-time 60 $INFRASTRUCTURE_DEPLOY_ENDPOINT) || {
          echo "❌ Failed to connect to deployment endpoint"
          exit 1
        }
        
        http_code=$(echo "$response" | tail -n1)
        body=$(echo "$response" | head -n -1)
        
        echo "HTTP Status: $http_code"
        echo "Response: $body"
        
        if [ "$http_code" -lt 200 ] || [ "$http_code" -ge 300 ]; then
          echo "❌ Deployment failed with status $http_code"
          echo "❌ Response body: $body"
          exit 1
        fi
        
        echo "✅ Deployment triggered successfully"