# Archivos de build y target
target/*
!target/*.jar
build/
out/

# Archivos de git
.git/
.gitignore

# IDEs
.idea/
.vscode/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
.metadata/
.factorypath
.apt_generated

# Logs
logs/
*.log

# OS específicos
.DS_Store
Thumbs.db

# Archivos temporales
*.tmp
*.temp
*.swp
*.swo
*~

# Archivos de configuración local
.env
application-dev.properties
application-local.properties

# Docker files
Dockerfile*
docker-compose*.yml
docker-compose*.yaml

# Maven wrapper jar (se descarga en build)
.mvn/wrapper/maven-wrapper.jar

# Tests y documentación
README.md
CLAUDE.md
*.md

# Archivos de respaldo
*.bak
*.backup

# Node modules (si hubiera frontend)
node_modules/
npm-debug.log*

# Coverage reports
coverage/
*.lcov

# Archivos de desarrollo Spring Boot
spring-boot-devtools.properties
HELP.md