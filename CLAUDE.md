# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive JWT-based authentication and authorization microservice built with Spring Boot 3.5.4 and Java 21 for the GEDSYS platform. It provides secure user authentication, session management, role-based access control, mobile integration, automatic username generation, and complete CI/CD pipeline with Docker deployment.

## Development Commands

### Build and Test
```bash
# Run the application (development with Docker Compose)
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev

# Clean build
./mvnw clean package

# Run tests (currently no tests implemented)
./mvnw test

# Build optimized Docker image (multi-stage)
docker build -t gedsys2-authentication .

# Build with Spring Boot (layered)
./mvnw spring-boot:build-image
```

### Database Operations
```bash
# Start development database
docker-compose up -d

# Check Flyway migration status
./mvnw flyway:info

# Flyway migration (manual, usually automatic on startup)
./mvnw flyway:migrate
```

### CI/CD and Docker
```bash
# Build local Docker image for testing
docker build -t gedsys2-auth:local .

# Run container locally
docker run -p 8080:8080 gedsys2-auth:local

# Check health status
curl http://localhost:8080/actuator/health

# Check application logs
docker logs <container-id>
```

## Architecture

### Core Components
- **AuthenticationService**: JWT token generation, validation, flexible login (username/email)
- **UserService**: User CRUD operations, automatic username generation, logical deletion
- **JwtService**: Token lifecycle management, session-aware expiration
- **PushTokenService**: Mobile device token management (FCM/APNS)
- **UsernameGenerator**: Automatic unique username creation from names
- **RefreshTokenService**: Refresh token rotation and session management

### Database Structure
- **User**: Main user entity with status management (ACTIVE, INACTIVE, DELETED)
- **RefreshToken**: Single-use token rotation with session tracking  
- **PushToken**: Mobile device notification tokens
- **Flyway Migrations**: V1-V8 covering complete schema evolution including username uniqueness

### Security Features
- Single session policy per session type (MOBILE/WEB)
- Role-based access control (USER/ADMIN)
- Session invalidation on password changes
- Logical deletion preserving audit trails
- Custom authentication entry point with localized error messages

## Configuration

### Environment Variables
```bash
# Database
DB_URL=**************************************************
DB_USERNAME=auth_user
DB_PASSWORD=auth_password

# JWT
JWT_SECRET=your-secret-key-here
JWT_ACCESS_EXPIRATION=3600000
JWT_MOBILE_ACCESS_EXPIRATION=7200000
JWT_WEB_ACCESS_EXPIRATION=3600000
JWT_REFRESH_EXPIRATION=604800000
```

### Application Profiles
- `dev`: Development with Docker Compose PostgreSQL
- `test`: Testing with H2 database
- Default: Production configuration

## API Endpoints

### Authentication
- `POST /auth/login` - Flexible login (username or email)
- `POST /auth/refresh` - Token refresh
- `POST /auth/logout` - Session logout
- `GET /auth/profile` - User profile
- `PUT /auth/change-password` - Password change

### Admin Operations (ADMIN role required)
- `GET /admin/users` - List users with pagination/filtering
- `POST /admin/users` - Create user
- `GET /admin/users/{id}` - Get user details
- `PUT /admin/users/{id}` - Update user

### Mobile Integration
- `POST /auth/push-token` - Register push token
- `GET /auth/push-token` - Get push token info

### Documentation
- Swagger UI: `http://localhost:8080/swagger-ui.html`
- API docs: `http://localhost:8080/v3/api-docs`
- OpenAPI specifications: `openapi.json` file in docs directory.

## Project Structure

```
├── .github
│   └── workflows
│       └── deploy.yml *
├── docs
│   ├── API.md *
│   ├── CI-CD-SETUP.md *
│   ├── DATABASE.md *
│   ├── IMPLEMENTATION_STATUS.md *
│   ├── openapi.json *
│   ├── PasswordRecovery.md *
│   ├── SWAGGER.md *
│   └── USER_MANAGEMENT.md *
├── src
│   ├── main
│   │   ├── java
│   │   │   └── co
│   │   │       └── com
│   │   │           └── gedsys
│   │   │               └── authentication
│   │   │                   ├── config
│   │   │                   │   ├── ApplicationStartupValidator.java * +
│   │   │                   │   ├── CustomAuthenticationEntryPoint.java * +
│   │   │                   │   ├── JwtAuthenticationFilter.java * +
│   │   │                   │   ├── JwtProperties.java * +
│   │   │                   │   ├── MetricsConfiguration.java * +
│   │   │                   │   ├── SecurityBeanConfig.java * +
│   │   │                   │   ├── SecurityConfiguration.java * +
│   │   │                   │   └── SwaggerConfig.java * +
│   │   │                   ├── controller
│   │   │                   │   └── v1
│   │   │                   │       ├── AdminControllerV1.java * +
│   │   │                   │       ├── AdminSessionControllerV1.java * +
│   │   │                   │       ├── AuthenticationControllerV1.java * +
│   │   │                   │       ├── HealthControllerV1.java * +
│   │   │                   │       ├── PasswordRecoveryControllerV1.java * +
│   │   │                   │       └── PushTokenControllerV1.java * +
│   │   │                   ├── dto
│   │   │                   │   ├── ActiveSessionInfo.java * +
│   │   │                   │   ├── AdminUserResponse.java * +
│   │   │                   │   ├── AuthResponse.java * +
│   │   │                   │   ├── ChangePasswordRequest.java * +
│   │   │                   │   ├── CreateUserRequest.java * +
│   │   │                   │   ├── DetailedSessionInfo.java * +
│   │   │                   │   ├── FullNameRequest.java * +
│   │   │                   │   ├── FullNameResponse.java * +
│   │   │                   │   ├── LoginRequest.java * +
│   │   │                   │   ├── PagedResponse.java * +
│   │   │                   │   ├── PasswordRecoveryCodeResponse.java * +
│   │   │                   │   ├── PasswordRecoveryErrorResponse.java * +
│   │   │                   │   ├── PasswordResetRequest.java * +
│   │   │                   │   ├── PasswordResetResponse.java * +
│   │   │                   │   ├── PushTokenInfo.java * +
│   │   │                   │   ├── PushTokenRequest.java * +
│   │   │                   │   ├── RefreshTokenRequest.java * +
│   │   │                   │   ├── UpdateUserRequest.java * +
│   │   │                   │   └── UserProfileResponse.java * +
│   │   │                   ├── entity
│   │   │                   │   ├── DeviceType.java * +
│   │   │                   │   ├── PasswordRecoveryCode.java * +
│   │   │                   │   ├── PushToken.java * +
│   │   │                   │   ├── RefreshToken.java * +
│   │   │                   │   ├── Role.java * +
│   │   │                   │   ├── SecurityAuditLog.java * +
│   │   │                   │   ├── SecurityEventType.java * +
│   │   │                   │   ├── SessionType.java * +
│   │   │                   │   ├── User.java * +
│   │   │                   │   └── UserStatus.java * +
│   │   │                   ├── exception
│   │   │                   │   ├── CodeGenerationException.java *
│   │   │                   │   ├── EmailAlreadyExistsException.java *
│   │   │                   │   ├── ErrorResponse.java * +
│   │   │                   │   ├── ExpiredRecoveryCodeException.java *
│   │   │                   │   ├── GlobalExceptionHandler.java * +
│   │   │                   │   ├── InvalidCredentialsException.java *
│   │   │                   │   ├── InvalidRecoveryCodeException.java *
│   │   │                   │   ├── InvalidTokenException.java *
│   │   │                   │   ├── RateLimitExceededException.java * +
│   │   │                   │   ├── TokenExpiredException.java *
│   │   │                   │   ├── UsedRecoveryCodeException.java *
│   │   │                   │   ├── UserDeletedException.java *
│   │   │                   │   ├── UserInactiveException.java * +
│   │   │                   │   ├── UsernameAlreadyExistsException.java *
│   │   │                   │   └── UserNotFoundException.java *
│   │   │                   ├── repository
│   │   │                   │   ├── PasswordRecoveryCodeRepository.java * +
│   │   │                   │   ├── PushTokenRepository.java * +
│   │   │                   │   ├── RefreshTokenRepository.java * +
│   │   │                   │   ├── SecurityAuditLogRepository.java * +
│   │   │                   │   └── UserRepository.java * +
│   │   │                   ├── service
│   │   │                   │   ├── AuthenticationService.java * +
│   │   │                   │   ├── HealthService.java * +
│   │   │                   │   ├── JwtService.java * +
│   │   │                   │   ├── PasswordRecoveryService.java * +
│   │   │                   │   ├── PushTokenService.java * +
│   │   │                   │   ├── RefreshTokenService.java * +
│   │   │                   │   ├── SecurityAuditService.java * +
│   │   │                   │   ├── UserDetailsServiceImpl.java * +
│   │   │                   │   ├── UsernameGenerator.java * +
│   │   │                   │   └── UserService.java * +
│   │   │                   ├── util
│   │   │                   │   ├── HttpRequestContextUtil.java * +
│   │   │                   │   ├── RateLimiter.java * +
│   │   │                   │   ├── SecureCodeGenerator.java * +
│   │   │                   │   └── SecurityConstants.java *
│   │   │                   └── AuthenticationApplication.java * +
│   │   └── resources
│   │       ├── db
│   │       │   └── migration
│   │       │       ├── V1__Create_users_table.sql *
│   │       │       ├── V2__Create_refresh_tokens_table.sql *
│   │       │       ├── V3__Create_push_tokens_table.sql *
│   │       │       ├── V4__fix_unique_constraint_for_session_policy.sql *
│   │       │       ├── V5__Insert_default_admin_user.sql *
│   │       │       ├── V6__add_session_id_to_refresh_tokens.sql *
│   │       │       ├── V7__Add_username_and_status_fields.sql *
│   │       │       ├── V8__Update_admin_username_and_add_unique_constraint.sql *
│   │       │       ├── V9__Add_session_metadata_to_refresh_tokens.sql *
│   │       │       └── V10__Create_password_recovery_tables.sql *
│   │       ├── application-prod.yml *
│   │       ├── application.yml *
│   │       ├── banner.txt *
│   │       └── logback-spring.xml *
│   └── test
│       ├── java
│       │   └── co
│       │       └── com
│       │           └── gedsys
│       │               └── authentication
│       │                   ├── config
│       │                   │   ├── ConfigurationValidationTest.java * +
│       │                   │   └── SecurityConfigurationTest.java * +
│       │                   └── service
│       │                       └── UserDetailsServiceImplTest.java * +
│       └── resources
│           └── application-test.properties *
├── .dockerignore *
├── .gitattributes *
├── .gitignore *
├── Dockerfile *
├── mvnw *
├── mvnw.cmd *
├── pom.xml *
└── README.md *


(* denotes selected files)
(+ denotes code-map available)
```

## Testing Strategy

**Note**: Actualmente no hay tests implementados en el proyecto. La estructura de testing descrita en el README es aspiracional.

### Test Structure (Planned)
- Unit tests para servicios y lógica de negocio
- Integration tests con Testcontainers para PostgreSQL
- Controller tests para endpoints REST

## Development Guidelines

### Code Conventions
- Java 21 modern features
- Spring Boot 3.5.4 patterns  
- Security-first approach
- Comprehensive error handling in Spanish
- Logical deletion over physical deletion

### Database Best Practices
- Use Flyway migrations for schema changes (V1-V8 currently implemented)
- Include deleted users in uniqueness checks
- Maintain audit trails with UserStatus enum

### Security Considerations
- Never expose JWT secrets in code
- Validate session types appropriately (MOBILE/WEB)
- Custom authentication entry point with localized messages
- Method-level security with @PreAuthorize annotations

## Recent Updates (Updated: 2025-01-18)

### CI/CD Pipeline Integration
- **GitHub Actions workflow** implementado en `.github/workflows/deploy.yml`
- **Docker multi-stage** optimizado con Alpine Linux para producción
- **Pipeline stages**: build, security scan (SpotBugs/FindSecBugs), Docker build multi-arch, deployment
- **Registry**: Configurado para `registry.gedsys.dev` con secrets management

### Enhanced Documentation
- **OpenAPI specifications** completas en `docs/openapi/`:
  - Documentación detallada para todos los endpoints
  - Ejemplos de request/response
  - Esquemas de validación completos
- **CI/CD Setup Guide** en `docs/CI-CD-SETUP.md`

### Logging and Monitoring Improvements
- **Enhanced logback configuration** con perfiles dev/prod
- **Health checks** integrados en Docker
- **Metrics configuration** optimizada para observabilidad

### Security Enhancements
- **SpotBugs + FindSecBugs** análisis estático integrado en CI
- **Non-root user** en imagen Docker
- **Secrets management** con GitHub Actions

## Default Admin Account
- Email: `<EMAIL>`
- Username: `admin` (auto-generated)
- Password: `admin123`
- Role: ADMIN
- Change immediately in production

## CI/CD Deployment

### Required Secrets
Configure en GitHub Settings → Secrets:
- `REGISTRY_USERNAME`: Usuario para registry.gedsys.dev
- `REGISTRY_PASSWORD`: Token de acceso al registry

### Deployment Strategy
- **Automatic deployment** en push a `main` branch
- **Manual approval** requerido para environment "production"
- **Multi-architecture images** (AMD64/ARM64)
- **Rolling updates** con health checks
- @changelog.md contiene el historial de cambios para consulta rapida
- Apply No comments policy in code source and clean code naming convention
- context-path predeterminado de la aplicacion: /identity