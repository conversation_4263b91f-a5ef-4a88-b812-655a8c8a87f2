#!/usr/bin/env node

/**
 * Script de prueba para flujo completo de autenticación
 * Requiere: Node.js 18+
 * Uso: node test-auth-flow.js
 */

import { spawn } from 'child_process';
import { writeFile, unlink, access } from 'fs/promises';
import { constants } from 'fs';

// Configuración
const CONFIG = {
    BASE_URL: 'http://localhost:8080/identity/api/v1',
    ADMIN_EMAIL: '<EMAIL>',
    ADMIN_PASSWORD: 'admin123',
    TEST_USER_EMAIL: '<EMAIL>',
    TEST_USER_PASSWORD: 'TestPass123!',
    TEST_USER_FIRST_NAME: 'Usuario',
    TEST_USER_LAST_NAME: 'Prueba',
    SERVER_WAIT_TIME: 60, // segundos
    LOG_FILE: 'server.log'
};

// Colores para output
const COLORS = {
    RED: '\x1b[31m',
    GREEN: '\x1b[32m',
    YELLOW: '\x1b[33m',
    BLUE: '\x1b[34m',
    RESET: '\x1b[0m'
};

// Estado global
let state = {
    totalTests: 0,
    successCount: 0,
    errorCount: 0,
    adminToken: '',
    userToken: '',
    userId: '',
    serverProcess: null,
    serverStartedByScript: false
};

// Funciones de utilidad para output
function printStep(message) {
    console.log(`${COLORS.BLUE}📋 PASO ${++state.totalTests}: ${message}${COLORS.RESET}`);
}

function printSuccess(message) {
    console.log(`${COLORS.GREEN}✅ ${message}${COLORS.RESET}`);
    state.successCount++;
}

function printError(message) {
    console.log(`${COLORS.RED}❌ ${message}${COLORS.RESET}`);
    state.errorCount++;
}

function printInfo(message) {
    console.log(`${COLORS.BLUE}ℹ️  ${message}${COLORS.RESET}`);
}

function printWarning(message) {
    console.log(`${COLORS.YELLOW}⚠️  ${message}${COLORS.RESET}`);
}

// Función para hacer peticiones HTTP
async function makeRequest(method, endpoint, data = null, authHeader = null, expectedStatus = 200) {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    if (authHeader) {
        options.headers['Authorization'] = `Bearer ${authHeader}`;
    }
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(url, options);
        const responseText = await response.text();
        
        let responseBody;
        try {
            responseBody = JSON.parse(responseText);
        } catch {
            responseBody = responseText;
        }
        
        if (response.status === expectedStatus) {
            return { success: true, data: responseBody, status: response.status };
        } else {
            printError(`Estado HTTP esperado: ${expectedStatus}, recibido: ${response.status}`);
            if (responseBody) {
                console.log(`Respuesta:`, JSON.stringify(responseBody, null, 2));
            }
            return { success: false, data: responseBody, status: response.status };
        }
    } catch (error) {
        printError(`Error de red: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// Función para verificar dependencias
async function checkDependencies() {
    printStep('Verificando dependencias');
    
    // Verificar Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.substring(1).split('.')[0]);
    
    if (majorVersion < 18) {
        printError(`Node.js ${majorVersion} detectado. Se requiere Node.js 18+`);
        process.exit(1);
    }
    
    // Verificar que fetch esté disponible (Node 18+)
    if (typeof fetch === 'undefined') {
        printError('fetch no está disponible. Se requiere Node.js 18+ o instalar node-fetch');
        process.exit(1);
    }
    
    printSuccess(`Dependencias verificadas (Node.js ${nodeVersion})`);
}

// Función para verificar si el archivo existe
async function fileExists(path) {
    try {
        await access(path, constants.F_OK);
        return true;
    } catch {
        return false;
    }
}

// Función para esperar
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Función para iniciar el servidor
async function startServer() {
    printStep('Verificando si el servidor está corriendo');
    
    // Verificar si ya está corriendo y detenerlo primero para limpiar la BD
    try {
        const response = await fetch(`${CONFIG.BASE_URL}/health/status`);
        if (response.ok) {
            printInfo('Servidor ya está corriendo. Deteniéndolo para reiniciar con BD limpia...');
            // Intentar detener el servidor existente
            await stopExistingServer();
            await sleep(3000); // Esperar que se detenga completamente
        }
    } catch {
        // Servidor no responde, está bien
    }
    
    printInfo('Servidor no está corriendo. Iniciando...');
    
    // Verificar que estamos en el directorio correcto
    if (!await fileExists('pom.xml')) {
        printError('No se encontró pom.xml. Ejecutar desde el directorio raíz del proyecto');
        process.exit(1);
    }
    
    // Iniciar servidor
    printInfo('Iniciando servidor con: mvn spring-boot:run -Dspring-boot.run.profiles=dev');
    
    const serverProcess = spawn('mvn', ['spring-boot:run', '-Dspring-boot.run.profiles=dev'], {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: false
    });
    
    state.serverProcess = serverProcess;
    state.serverStartedByScript = true;
    
    // Capturar logs
    const logStream = await writeFile(CONFIG.LOG_FILE, '', { flag: 'w' });
    
    serverProcess.stdout.on('data', async (data) => {
        await writeFile(CONFIG.LOG_FILE, data, { flag: 'a' }).catch(() => {});
    });
    
    serverProcess.stderr.on('data', async (data) => {
        await writeFile(CONFIG.LOG_FILE, data, { flag: 'a' }).catch(() => {});
    });
    
    printInfo(`Servidor iniciado con PID: ${serverProcess.pid}`);
    printInfo('Esperando que el servidor esté listo...');
    
    // Esperar hasta que el servidor responda
    const maxAttempts = CONFIG.SERVER_WAIT_TIME;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
        try {
            const response = await fetch(`${CONFIG.BASE_URL}/health/status`);
            if (response.ok) {
                printSuccess(`Servidor listo después de ${attempts} segundos`);
                return;
            }
        } catch {
            // Continuar esperando
        }
        
        // Verificar si el proceso sigue corriendo
        if (serverProcess.exitCode !== null) {
            printError('El servidor se detuvo inesperadamente');
            if (await fileExists(CONFIG.LOG_FILE)) {
                printError('Últimas líneas del log:');
                // En un entorno real, podrías leer las últimas líneas del log
                console.log('Ver server.log para detalles');
            }
            process.exit(1);
        }
        
        await sleep(1000);
        attempts++;
        
        // Mostrar progreso cada 10 segundos
        if (attempts % 10 === 0) {
            printInfo(`Esperando servidor... (${attempts}/${maxAttempts})`);
        }
    }
    
    printError(`Timeout esperando que el servidor responda después de ${maxAttempts} segundos`);
    await stopServer();
    process.exit(1);
}

// Función para detener servidor existente (no iniciado por este script)
async function stopExistingServer() {
    printInfo('Intentando detener servidor existente...');
    
    return new Promise((resolve) => {
        try {
            const findProcess = spawn('pgrep', ['-f', 'spring-boot:run'], {
                stdio: ['ignore', 'pipe', 'pipe']
            });
            
            let pids = '';
            findProcess.stdout.on('data', (data) => {
                pids += data.toString();
            });
            
            findProcess.on('close', async (code) => {
                if (code === 0 && pids.trim()) {
                    const pidList = pids.trim().split('\n').filter(pid => pid);
                    printInfo(`Encontrados ${pidList.length} procesos Spring Boot. Deteniéndolos...`);
                    
                    for (const pid of pidList) {
                        try {
                            process.kill(parseInt(pid), 'SIGTERM');
                            printInfo(`Proceso ${pid} terminado`);
                        } catch (error) {
                            printWarning(`No se pudo terminar proceso ${pid}: ${error.message}`);
                        }
                    }
                } else {
                    printInfo('No se encontraron procesos Spring Boot corriendo');
                }
                resolve();
            });
            
            findProcess.on('error', (error) => {
                printWarning(`Error buscando procesos: ${error.message}`);
                resolve();
            });
            
        } catch (error) {
            printWarning(`Error intentando detener servidor existente: ${error.message}`);
            resolve();
        }
    });
}

// Función para detener el servidor
async function stopServer() {
    if (state.serverStartedByScript && state.serverProcess) {
        printInfo(`Deteniendo servidor (PID: ${state.serverProcess.pid})...`);
        
        // Intentar detener gracefully
        try {
            process.kill(state.serverProcess.pid, 'SIGTERM');
            
            // Esperar un poco
            await sleep(3000);
            
            // Si sigue corriendo, forzar terminación
            if (state.serverProcess.exitCode === null) {
                printInfo('Forzando terminación del servidor...');
                process.kill(state.serverProcess.pid, 'SIGKILL');
            }
            
            printInfo('Servidor detenido');
        } catch (error) {
            printWarning(`Error al detener servidor: ${error.message}`);
        }
        
        // Limpiar archivo de log
        try {
            await unlink(CONFIG.LOG_FILE);
        } catch {
            // Ignorar errores de limpieza
        }
    }
}

// 1. Login con admin
async function loginAdmin() {
    printStep('Login con administrador');
    
    const loginData = {
        identifier: CONFIG.ADMIN_EMAIL,
        password: CONFIG.ADMIN_PASSWORD,
        sessionType: 'WEB'
    };
    
    const result = await makeRequest('POST', '/auth/login', loginData, null, 200);
    
    if (result.success) {
        state.adminToken = result.data.accessToken;
        if (state.adminToken) {
            printSuccess('Login admin exitoso. Token obtenido.');
            printInfo(`Token: ${state.adminToken.substring(0, 50)}...`);
        } else {
            printError('No se pudo obtener el token de admin');
            process.exit(1);
        }
    } else {
        printError('Fallo en login de administrador');
        process.exit(1);
    }
}

// 2. Crear usuario con rol USER
async function createUser() {
    printStep('Crear usuario con rol USER');
    
    const userData = {
        email: CONFIG.TEST_USER_EMAIL,
        password: CONFIG.TEST_USER_PASSWORD,
        firstName: CONFIG.TEST_USER_FIRST_NAME,
        lastName: CONFIG.TEST_USER_LAST_NAME,
        role: 'USER'
    };
    
    const result = await makeRequest('POST', '/admin/users', userData, state.adminToken, 201);
    
    if (result.success) {
        state.userId = result.data.id;
        const username = result.data.username;
        printSuccess(`Usuario creado exitosamente. ID: ${state.userId}, Username: ${username}`);
    } else {
        printError('Fallo al crear usuario');
        process.exit(1);
    }
}

// 3. Login exitoso con nuevo usuario
async function loginNewUser() {
    printStep('Login con nuevo usuario');
    
    const loginData = {
        identifier: CONFIG.TEST_USER_EMAIL,
        password: CONFIG.TEST_USER_PASSWORD,
        sessionType: 'WEB'
    };
    
    const result = await makeRequest('POST', '/auth/login', loginData, null, 200);
    
    if (result.success) {
        state.userToken = result.data.accessToken;
        const userRole = result.data.user.role;
        printSuccess(`Login usuario exitoso. Rol: ${userRole}`);
        printInfo(`Token usuario: ${state.userToken.substring(0, 50)}...`);
    } else {
        printError('Fallo en login de usuario');
        process.exit(1);
    }
}

// 4. Deshabilitar usuario
async function disableUser() {
    printStep('Deshabilitar usuario');
    
    const disableData = { enabled: false };
    
    const result = await makeRequest('PATCH', `/admin/users/${state.userId}/enabled`, disableData, state.adminToken, 200);
    
    if (result.success) {
        const enabled = result.data.enabled;
        printSuccess(`Usuario deshabilitado. Estado: ${enabled}`);
    } else {
        printError('Fallo al deshabilitar usuario');
        process.exit(1);
    }
}

// 5. Intentar login con usuario deshabilitado (debe fallar)
async function loginDisabledUser() {
    printStep('Intentar login con usuario deshabilitado (debe fallar)');
    
    const loginData = {
        identifier: CONFIG.TEST_USER_EMAIL,
        password: CONFIG.TEST_USER_PASSWORD,
        sessionType: 'WEB'
    };
    
    const result = await makeRequest('POST', '/auth/login', loginData, null, 401); // Changed to 401 as the system returns this for disabled users
    
    if (result.success) {
        printSuccess('Login correctamente rechazado para usuario deshabilitado');
    } else {
        printError('El login no fue rechazado como se esperaba');
    }
}

// 6. Habilitar usuario nuevamente
async function enableUser() {
    printStep('Habilitar usuario nuevamente');
    
    const enableData = { enabled: true };
    
    const result = await makeRequest('PATCH', `/admin/users/${state.userId}/enabled`, enableData, state.adminToken, 200);
    
    if (result.success) {
        const enabled = result.data.enabled;
        printSuccess(`Usuario habilitado. Estado: ${enabled}`);
    } else {
        printError('Fallo al habilitar usuario');
        process.exit(1);
    }
}

// 7. Login exitoso después de rehabilitación
async function loginEnabledUser() {
    printStep('Login después de rehabilitación');
    
    const loginData = {
        identifier: CONFIG.TEST_USER_EMAIL,
        password: CONFIG.TEST_USER_PASSWORD,
        sessionType: 'WEB'
    };
    
    const result = await makeRequest('POST', '/auth/login', loginData, null, 200);
    
    if (result.success) {
        const userEmail = result.data.user.email;
        printSuccess(`Login exitoso después de rehabilitación. Usuario: ${userEmail}`);
    } else {
        printError('Fallo en login después de rehabilitación');
        process.exit(1);
    }
}

// Función de limpieza
async function cleanup() {
    printInfo('Iniciando limpieza...');
    
    // Eliminar usuario de prueba si existe
    if (state.userId && state.adminToken) {
        printInfo('Eliminando usuario de prueba...');
        try {
            const result = await makeRequest('DELETE', `/admin/users/${state.userId}`, null, state.adminToken, 200);
            if (result.success) {
                printInfo('Usuario de prueba eliminado');
            }
        } catch {
            // Ignorar errores de limpieza
        }
    }
    
    // Detener servidor si fue iniciado por este script
    await stopServer();
}

// Función para mostrar resumen final
function showSummary() {
    console.log('');
    console.log('=================================');
    console.log(`${COLORS.BLUE}📊 RESUMEN DE PRUEBAS${COLORS.RESET}`);
    console.log('=================================');
    console.log(`Total de pruebas: ${state.totalTests}`);
    console.log(`${COLORS.GREEN}Exitosas: ${state.successCount}${COLORS.RESET}`);
    console.log(`${COLORS.RED}Fallidas: ${state.errorCount}${COLORS.RESET}`);
    console.log('=================================');
    
    if (state.errorCount === 0) {
        console.log(`${COLORS.GREEN}🎉 TODAS LAS PRUEBAS PASARON${COLORS.RESET}`);
        return 0;
    } else {
        console.log(`${COLORS.RED}💥 ALGUNAS PRUEBAS FALLARON${COLORS.RESET}`);
        return 1;
    }
}

// Función principal
async function main() {
    console.log(`${COLORS.BLUE}🚀 INICIANDO PRUEBAS DE FLUJO DE AUTENTICACIÓN${COLORS.RESET}`);
    console.log('==================================================');
    
    try {
        // Ejecutar pruebas
        await checkDependencies();
        await startServer();
        await loginAdmin();
        await createUser();
        await loginNewUser();
        await disableUser();
        await loginDisabledUser();
        await enableUser();
        await loginEnabledUser();
        
        // Mostrar resumen
        const exitCode = showSummary();
        
        // Cleanup al final
        await cleanup();
        
        process.exit(exitCode);
        
    } catch (error) {
        printError(`Error inesperado: ${error.message}`);
        await cleanup();
        process.exit(1);
    }
}

// Manejar interrupciones
process.on('SIGINT', async () => {
    console.log('\n');
    printWarning('Interrupción detectada. Limpiando...');
    await cleanup();
    process.exit(1);
});

process.on('SIGTERM', async () => {
    printWarning('Terminación detectada. Limpiando...');
    await cleanup();
    process.exit(1);
});

// Ejecutar main
main().catch(async (error) => {
    printError(`Error fatal: ${error.message}`);
    await cleanup();
    process.exit(1);
});