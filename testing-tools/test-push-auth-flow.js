#!/usr/bin/env node

/**
 * Script de prueba para flujo de autenticación con push token
 * Requiere: Node.js 18+
 * Uso: node test-push-auth-flow.js
 */

import { spawn } from 'child_process';
import { writeFile, unlink, access } from 'fs/promises';
import { constants } from 'fs';

// Configuración
const CONFIG = {
    BASE_URL: 'http://localhost:8080/identity/api/v1',
    ADMIN_EMAIL: '<EMAIL>',
    ADMIN_PASSWORD: 'admin123',
    DEFAULT_PUSH_TOKEN: 'test-push-token-12345',
    DEFAULT_DEVICE_ID: 'test-device-id-67890',
    DEFAULT_DEVICE_TYPE: 'IOS',
    SERVER_WAIT_TIME: 60, // segundos
    LOG_FILE: 'server.log'
};

// Colores para output
const COLORS = {
    RED: '\\x1b[31m',
    GREEN: '\\x1b[32m',
    YELLOW: '\\x1b[33m',
    BLUE: '\\x1b[34m',
    RESET: '\\x1b[0m'
};

// Estado global
let state = {
    totalTests: 0,
    successCount: 0,
    errorCount: 0,
    adminToken: '',
    serverProcess: null,
    serverStartedByScript: false
};

// Funciones de utilidad para output
function printStep(message) {
    console.log(`${COLORS.BLUE}📋 PASO ${++state.totalTests}: ${message}${COLORS.RESET}`);
}

function printSuccess(message) {
    console.log(`${COLORS.GREEN}✅ ${message}${COLORS.RESET}`);
    state.successCount++;
}

function printError(message) {
    console.log(`${COLORS.RED}❌ ${message}${COLORS.RESET}`);
    state.errorCount++;
}

function printInfo(message) {
    console.log(`${COLORS.BLUE}ℹ️  ${message}${COLORS.RESET}`);
}

function printWarning(message) {
    console.log(`${COLORS.YELLOW}⚠️  ${message}${COLORS.RESET}`);
}

// Función para hacer peticiones HTTP
async function makeRequest(method, endpoint, data = null, authHeader = null, expectedStatus = 200) {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    if (authHeader) {
        options.headers['Authorization'] = `Bearer ${authHeader}`;
    }
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(url, options);
        const responseText = await response.text();
        
        let responseBody;
        try {
            responseBody = JSON.parse(responseText);
        } catch {
            responseBody = responseText;
        }
        
        if (response.status === expectedStatus) {
            return { success: true, data: responseBody, status: response.status };
        } else {
            printError(`Estado HTTP esperado: ${expectedStatus}, recibido: ${response.status}`);
            if (responseBody) {
                console.log(`Respuesta:`, JSON.stringify(responseBody, null, 2));
            }
            return { success: false, data: responseBody, status: response.status };
        }
    } catch (error) {
        printError(`Error de red: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// Función para verificar dependencias
async function checkDependencies() {
    printStep('Verificando dependencias');
    
    // Verificar Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.substring(1).split('.')[0]);
    
    if (majorVersion < 18) {
        printError(`Node.js ${majorVersion} detectado. Se requiere Node.js 18+`);
        process.exit(1);
    }
    
    // Verificar que fetch esté disponible (Node 18+)
    if (typeof fetch === 'undefined') {
        printError('fetch no está disponible. Se requiere Node.js 18+ o instalar node-fetch');
        process.exit(1);
    }
    
    printSuccess(`Dependencias verificadas (Node.js ${nodeVersion})`);
}

// Función para verificar si el archivo existe
async function fileExists(path) {
    try {
        await access(path, constants.F_OK);
        return true;
    } catch {
        return false;
    }
}

// Función para esperar
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Función para iniciar el servidor
async function startServer() {
    printStep('Verificando si el servidor está corriendo');
    
    // Verificar si ya está corriendo
    try {
        const response = await fetch(`${CONFIG.BASE_URL}/health/status`);
        if (response.ok) {
            printInfo('Servidor ya está corriendo.');
            return;
        }
    } catch {
        // Servidor no responde, está bien
    }
    
    printInfo('Servidor no está corriendo. Iniciando...');
    
    // Verificar que estamos en el directorio correcto
    if (!await fileExists('pom.xml')) {
        printError('No se encontró pom.xml. Ejecutar desde el directorio raíz del proyecto');
        process.exit(1);
    }
    
    // Iniciar servidor
    printInfo('Iniciando servidor con: mvn spring-boot:run -Dspring-boot.run.profiles=dev');
    
    const serverProcess = spawn('mvn', ['spring-boot:run', '-Dspring-boot.run.profiles=dev'], {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: false
    });
    
    state.serverProcess = serverProcess;
    state.serverStartedByScript = true;
    
    // Capturar logs
    serverProcess.stdout.on('data', async (data) => {
        await writeFile(CONFIG.LOG_FILE, data, { flag: 'a' }).catch(() => {});
    });
    
    serverProcess.stderr.on('data', async (data) => {
        await writeFile(CONFIG.LOG_FILE, data, { flag: 'a' }).catch(() => {});
    });
    
    printInfo(`Servidor iniciado con PID: ${serverProcess.pid}`);
    printInfo('Esperando que el servidor esté listo...');
    
    // Esperar hasta que el servidor responda
    const maxAttempts = CONFIG.SERVER_WAIT_TIME;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
        try {
            const response = await fetch(`${CONFIG.BASE_URL}/health/status`);
            if (response.ok) {
                printSuccess(`Servidor listo después de ${attempts} segundos`);
                return;
            }
        } catch {
            // Continuar esperando
        }
        
        // Verificar si el proceso sigue corriendo
        if (serverProcess.exitCode !== null) {
            printError('El servidor se detuvo inesperadamente');
            if (await fileExists(CONFIG.LOG_FILE)) {
                printError('Últimas líneas del log:');
                // En un entorno real, podrías leer las últimas líneas del log
                console.log('Ver server.log para detalles');
            }
            process.exit(1);
        }
        
        await sleep(1000);
        attempts++;
        
        // Mostrar progreso cada 10 segundos
        if (attempts % 10 === 0) {
            printInfo(`Esperando servidor... (${attempts}/${maxAttempts})`);
        }
    }
    
    printError(`Timeout esperando que el servidor responda después de ${maxAttempts} segundos`);
    await stopServer();
    process.exit(1);
}

// Función para detener el servidor
async function stopServer() {
    if (state.serverStartedByScript && state.serverProcess) {
        printInfo(`Deteniendo servidor (PID: ${state.serverProcess.pid})...`);
        
        try {
            // Intentar detener gracefully primero
            state.serverProcess.kill('SIGTERM');

            // Esperar a que el proceso termine gracefully
            let attempts = 0;
            const maxWaitAttempts = 15; // 15 segundos

            while (attempts < maxWaitAttempts && state.serverProcess.exitCode === null) {
                await sleep(1000);
                attempts++;

                if (attempts % 5 === 0) {
                    printInfo(`Esperando terminación graceful... (${attempts}/${maxWaitAttempts})`);
                }
            }

            // Si aún está corriendo después del timeout, forzar terminación
            if (state.serverProcess.exitCode === null) {
                printInfo('Forzando terminación del servidor...');
                state.serverProcess.kill('SIGKILL');

                // Esperar un poco más para la terminación forzada
                await sleep(2000);
            }
            
            printInfo('Servidor detenido');
        } catch (error) {
            printWarning(`Error al detener servidor: ${error.message}`);
        }
        
        // Limpiar archivo de log
        try {
            await unlink(CONFIG.LOG_FILE);
        } catch {
            // Ignorar errores de limpieza
        }
    }
}

// 1. Login with admin mobile (IOS) with deviceId
async function loginAdminMobile() {
    printStep('Login con administrador mobile (IOS) y deviceId (sin push token)');

    const loginData = {
        identifier: CONFIG.ADMIN_EMAIL,
        password: CONFIG.ADMIN_PASSWORD,
        sessionType: 'MOBILE',
        deviceType: CONFIG.DEFAULT_DEVICE_TYPE,
        deviceId: CONFIG.DEFAULT_DEVICE_ID
        // pushToken removed - will be registered separately in registerPushToken()
    };
    
    const result = await makeRequest('POST', '/auth/login', loginData, null, 200);
    
    if (result.success) {
        state.adminToken = result.data.accessToken;
        if (state.adminToken) {
            printSuccess('Login admin mobile exitoso. Token obtenido.');
            printInfo(`Token: ${state.adminToken.substring(0, 50)}...`);
            printInfo(`Session Type: ${result.data.sessionType || 'N/A'}`);
            printInfo(`User Role: ${result.data.user?.role || 'N/A'}`);
            printInfo('Push token NO enviado durante login - será registrado por separado');
        } else {
            printError('No se pudo obtener el token de admin');
            process.exit(1);
        }
    } else {
        printError('Fallo en login de administrador mobile');
        process.exit(1);
    }
}

// 2. Registrar token push adicional (simulando que el dispositivo actualiza su token)
async function registerPushToken() {
    printStep('Registrar token push con mobile token (actualización de token)');
    
    const pushTokenData = {
        pushToken: CONFIG.DEFAULT_PUSH_TOKEN + '-updated',
        deviceType: CONFIG.DEFAULT_DEVICE_TYPE,
        deviceId: CONFIG.DEFAULT_DEVICE_ID
    };
    
    const result = await makeRequest('POST', '/push-token', pushTokenData, state.adminToken, 200);
    
    if (result.success) {
        printSuccess('Token push registrado exitosamente.');
        printInfo(`Push Token: ${result.data.pushToken?.token || 'N/A'}`);
        printInfo(`Device Type: ${result.data.pushToken?.deviceType || 'N/A'}`);
        printInfo(`Device ID: ${result.data.pushToken?.deviceId || 'N/A'}`);
    } else {
        printError('Fallo al registrar token push');
        process.exit(1);
    }
}

// 3. Consultar token push registrado
async function getPushToken() {
    printStep('Consultar token push registrado');
    
    const result = await makeRequest('GET', '/push-token', null, state.adminToken, 200);
    
    if (result.success) {
        printSuccess('Token push consultado exitosamente.');
        if (result.data.pushToken) {
            printInfo(`Push Token: ${result.data.pushToken.token || 'N/A'}`);
            printInfo(`Device Type: ${result.data.pushToken.deviceType || 'N/A'}`);
            printInfo(`Device ID: ${result.data.pushToken.deviceId || 'N/A'}`);
        } else {
            printInfo(`Push Token: No token registered`);
        }
    } else {
        printError('Fallo al consultar token push');
        process.exit(1);
    }
}

// 4. Hacer logout mobile
async function logoutMobile() {
    printStep('Logout mobile');
    
    const logoutData = {
        sessionType: 'MOBILE'
    };
    
    const result = await makeRequest('POST', '/auth/logout', logoutData, state.adminToken, 200);
    
    if (result.success) {
        printSuccess('Logout mobile exitoso.');
    } else {
        printError('Fallo en logout mobile');
        process.exit(1);
    }
}

// Función para mostrar resumen final
function showSummary() {
    console.log('');
    console.log('=================================');
    console.log(`${COLORS.BLUE}📊 RESUMEN DE PRUEBAS${COLORS.RESET}`);
    console.log('=================================');
    console.log(`Total de pruebas: ${state.totalTests}`);
    console.log(`${COLORS.GREEN}Exitosas: ${state.successCount}${COLORS.RESET}`);
    console.log(`${COLORS.RED}Fallidas: ${state.errorCount}${COLORS.RESET}`);
    console.log('=================================');
    
    if (state.errorCount === 0) {
        console.log(`${COLORS.GREEN}🎉 TODAS LAS PRUEBAS PASARON${COLORS.RESET}`);
        return 0;
    } else {
        console.log(`${COLORS.RED}💥 ALGUNAS PRUEBAS FALLARON${COLORS.RESET}`);
        return 1;
    }
}

// Función principal
async function main() {
    console.log(`${COLORS.BLUE}🚀 INICIANDO PRUEBAS DE FLUJO DE AUTENTICACIÓN CON PUSH TOKEN${COLORS.RESET}`);
    console.log('==================================================');
    
    try {
        // Ejecutar pruebas
        await checkDependencies();
        await startServer();
        await loginAdminMobile();
        await registerPushToken();
        await getPushToken();
        await logoutMobile();
        
        // Mostrar resumen
        const exitCode = showSummary();
        
        // Cleanup al final
        await cleanup();
        
        process.exit(exitCode);
        
    } catch (error) {
        printError(`Error inesperado: ${error.message}`);
        await cleanup();
        process.exit(1);
    }
}

// Función de limpieza
async function cleanup() {
    printInfo('Iniciando limpieza...');
    
    // Detener servidor si fue iniciado por este script
    await stopServer();
}

// Manejar interrupciones
process.on('SIGINT', async () => {
    console.log('\\n');
    printWarning('Interrupción detectada. Limpiando...');
    await cleanup();
    process.exit(1);
});

process.on('SIGTERM', async () => {
    printWarning('Terminación detectada. Limpiando...');
    await cleanup();
    process.exit(1);
});

// Ejecutar main
main().catch(async (error) => {
    printError(`Error fatal: ${error.message}`);
    await cleanup();
    process.exit(1);
});