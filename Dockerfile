# Dockerfile optimizado para Spring Boot con Java 21
# Usa JAR pre-compilado del CI para mayor eficiencia

FROM eclipse-temurin:21-jre-alpine AS base

# Instalar curl, crear usuario no-root y directorios necesarios
RUN apk add --no-cache curl && \
    addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup && \
    mkdir -p /app/logs && \
    chown appuser:appgroup /app/logs

# Configurar directorio de trabajo
WORKDIR /app

# Capa de aplicación - Esta capa se invalidará en builds sin caché
COPY --chown=appuser:appgroup --chmod=444 target/*.jar app.jar
RUN echo "Application layer with JAR file" > /tmp/app-layer-marker

# Cambiar a usuario no-root
USER appuser

# Configurar variables de entorno por defecto
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS=""

# Puerto de la aplicación
EXPOSE 8080

# Health check nativo de Docker
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# Comando optimizado para contenedores con JVM tuning
ENTRYPOINT ["sh", "-c", "exec java \
    -server \
    -XX:+UseContainerSupport \
    -XX:MaxRAMPercentage=75.0 \
    -XX:InitialRAMPercentage=50.0 \
    -XX:+UseG1GC \
    -XX:+UseStringDeduplication \
    -XX:+OptimizeStringConcat \
    -XX:+UseCompressedOops \
    -XX:+UseCompressedClassPointers \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.jmx.enabled=false \
    -Dmanagement.endpoint.jmx.exposure.exclude=* \
    -Djava.awt.headless=true \
    -Dfile.encoding=UTF-8 \
    -Duser.timezone=UTC \
    $JAVA_OPTS \
    -jar app.jar"]

# Metadata de la imagen
LABEL maintainer="GEDSYS Team"
LABEL description="GEDSYS Authentication Service - Spring Boot 3.5.4 with Java 21"
LABEL version="1.0"
LABEL java.version="21"
LABEL spring.boot.version="3.5.4"