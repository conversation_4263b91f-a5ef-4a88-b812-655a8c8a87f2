-- Add session_id column to refresh_tokens table for access token invalidation
-- This enables proper single session policy by linking access tokens to active refresh tokens

-- Add session_id column (nullable initially for existing data)
ALTER TABLE refresh_tokens 
ADD COLUMN session_id VARCHAR(36);

-- Generate unique session_id for existing records
UPDATE refresh_tokens 
SET session_id = gen_random_uuid()::text 
WHERE session_id IS NULL;

-- Make session_id NOT NULL after populating existing records
ALTER TABLE refresh_tokens 
ALTER COLUMN session_id SET NOT NULL;

-- Add unique constraint on session_id
ALTER TABLE refresh_tokens 
ADD CONSTRAINT uk_refresh_tokens_session_id UNIQUE (session_id);

-- Add index for performance on session_id lookups
CREATE INDEX idx_refresh_tokens_session_id ON refresh_tokens (session_id);

-- Add composite index for efficient session validation queries
CREATE INDEX idx_refresh_tokens_user_session_type_session_id 
ON refresh_tokens (user_id, session_type, session_id) 
WHERE used = false;
