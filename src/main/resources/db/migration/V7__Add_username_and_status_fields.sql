-- Add username and status fields to users table for enhanced user management
-- This migration supports automatic username generation and logical deletion

-- Add username field (initially nullable for existing users)
ALTER TABLE users ADD COLUMN username VARCHAR(100);

-- Add status field with default value for existing users
ALTER TABLE users ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' 
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED'));

-- Migrate existing data: set status based on enabled field
UPDATE users SET status = 'ACTIVE' WHERE enabled = true;
UPDATE users SET status = 'INACTIVE' WHERE enabled = false;

-- Create indexes for performance optimization
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- Add unique constraint for username (will be enforced after data migration)
-- Note: This will be done in a separate step after username generation

-- Add comments for documentation
COMMENT ON COLUMN users.username IS 'Unique username generated from first and last name';
COMMENT ON COLUMN users.status IS 'User status: ACTIVE, INACTIVE, or DELETED for logical deletion';

-- Note: Username generation for existing users will be handled by the application
-- during the first startup after this migration. The username field will be made
-- NOT NULL and UNIQUE in a subsequent migration after all usernames are generated.
