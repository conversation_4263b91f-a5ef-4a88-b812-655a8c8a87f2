-- Create password_recovery_codes table
CREATE TABLE password_recovery_codes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    code VARCHAR2(8) NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN NOT NULL DEFAULT FALSE,
    used_at TIMESTAMP NULL,
    request_ip VARCHAR2(45) NULL,
    usage_ip VARCHAR2(45) NULL,
    session_id VARCHAR2(100) NULL,
    CONSTRAINT fk_password_recovery_codes_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for password_recovery_codes
CREATE INDEX idx_password_recovery_codes_user_id ON password_recovery_codes(user_id);
CREATE INDEX idx_password_recovery_codes_code ON password_recovery_codes(code);
CREATE INDEX idx_password_recovery_codes_created_at ON password_recovery_codes(created_at);
CREATE INDEX idx_password_recovery_codes_expires_at ON password_recovery_codes(expires_at);
CREATE INDEX idx_password_recovery_codes_used ON password_recovery_codes(used);

-- Create security_audit_log table
CREATE TABLE security_audit_log (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NULL,
    event_type VARCHAR2(100) NOT NULL,
    event_description TEXT NOT NULL,
    event_details JSONB NULL,
    ip_address VARCHAR2(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_security_audit_log_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for security_audit_log
CREATE INDEX idx_security_audit_log_user_id ON security_audit_log(user_id);
CREATE INDEX idx_security_audit_log_event_type ON security_audit_log(event_type);
CREATE INDEX idx_security_audit_log_created_at ON security_audit_log(created_at);
CREATE INDEX idx_security_audit_log_ip_address ON security_audit_log(ip_address);