-- Update admin username and add unique constraint to username field
-- This migration ensures the default admin user has a proper username and enforces uniqueness

-- Step 1: Update the default admin user with a username
UPDATE users 
SET username = 'admin', 
    updated_at = CURRENT_TIMESTAMP 
WHERE email = '<EMAIL>' 
  AND username IS NULL;

-- Step 2: Generate usernames for any other existing users without username
-- This ensures all existing users have usernames before adding the unique constraint
UPDATE users 
SET username = CONCAT(
    LOWER(REGEXP_REPLACE(first_name, '[^a-zA-Z]', '', 'g')),
    '.',
    LOWER(REGEXP_REPLACE(last_name, '[^a-zA-Z]', '', 'g'))
),
updated_at = CURRENT_TIMESTAMP
WHERE username IS NULL;

-- Step 3: Handle potential duplicates by adding a numeric suffix
-- This ensures uniqueness before applying the constraint
WITH duplicates AS (
    SELECT username, ROW_NUMBER() OVER (PARTITION BY username ORDER BY id) as rn
    FROM users
    WHERE username IS NOT NULL
)
UPDATE users 
SET username = CONC<PERSON>(users.username, duplicates.rn),
    updated_at = CURRENT_TIMESTAMP
FROM duplicates
WHERE users.username = duplicates.username 
  AND duplicates.rn > 1;

-- Step 4: Make username NOT NULL and add unique constraint
ALTER TABLE users ALTER COLUMN username SET NOT NULL;
ALTER TABLE users ADD CONSTRAINT uk_users_username UNIQUE (username);

-- Step 5: Add comment for documentation
COMMENT ON CONSTRAINT uk_users_username ON users IS 'Ensures username uniqueness across all users including deleted ones';

-- Verification query (commented out for production)
-- SELECT id, email, username, first_name, last_name, status FROM users WHERE email = '<EMAIL>';