-- Add session metadata fields to refresh_tokens table for detailed session tracking
-- This enables comprehensive session monitoring including IP address, user agent, and activity tracking

-- Add client_ip_address column for tracking client IP addresses
ALTER TABLE refresh_tokens 
ADD COLUMN client_ip_address VARCHAR(45);

-- Add user_agent column for tracking client user agents (max length for typical user agents)
ALTER TABLE refresh_tokens 
ADD COLUMN user_agent TEXT;

-- Add last_activity_at column for tracking session activity
ALTER TABLE refresh_tokens 
ADD COLUMN last_activity_at TIMESTAMP WITH TIME ZONE;

-- Set initial last_activity_at to created_at for existing records
UPDATE refresh_tokens 
SET last_activity_at = created_at 
WHERE last_activity_at IS NULL;

-- Add index for efficient IP address queries (for security monitoring)
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_client_ip ON refresh_tokens (client_ip_address);

-- Add index for efficient last activity queries (for session cleanup and monitoring)
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_last_activity ON refresh_tokens (last_activity_at);

-- Add composite index for efficient session monitoring queries (without volatile function)
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_last_activity_open
ON refresh_tokens (user_id, last_activity_at DESC)
WHERE used = false;

-- Add index for expiry date queries (for session cleanup)
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expiry ON refresh_tokens (expiry_date);

-- Add comment to document the purpose of new columns
COMMENT ON COLUMN refresh_tokens.client_ip_address IS 'IP address of the client that created this session';
COMMENT ON COLUMN refresh_tokens.user_agent IS 'User agent string of the client that created this session';
COMMENT ON COLUMN refresh_tokens.last_activity_at IS 'Timestamp of the last activity for this session';
