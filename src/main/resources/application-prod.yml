# Production Configuration Profile
# Only overrides necessary settings from application.yml

# Production Logging - Minimized for performance and security
logging:
  level:
    co.com.gedsys.authentication: warn
    org.springframework.security: warn
    org.flywaydb: warn
    org.hibernate.SQL: error
    org.hibernate.type.descriptor.sql.BasicBinder: error
    root: info

# Security - Disable API documentation in production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# Actuator - Restricted endpoints for production
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized

# JPA - Production optimizations
spring:
  jpa:
    show-sql: false
    properties:
      hibernate:
        generate_statistics: false
        format_sql: false