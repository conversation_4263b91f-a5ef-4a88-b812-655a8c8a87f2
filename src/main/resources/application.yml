# Server Configuration
server:
  servlet:
    context-path: /identity

# Database Configuration
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:prod}
  datasource:
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 600000
      leak-detection-threshold: 60000
  devtools:
    add-properties: false

  # JPA/Hibernate Configuration
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        jdbc:
          time_zone: UTC

  # Flyway Configuration
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    out-of-order: false
    clean-disabled: true

  # Docker Compose Configuration (disabled by default)
  docker:
    compose:
      enabled: false

# JWT Settings - Configurable
app:
  jwt:
    secret: ${JWT_SECRET}
    access-token-expiration: ${JWT_ACCESS_EXPIRATION:900000}
    refresh-token-expiration: ${JWT_REFRESH_EXPIRATION:604800000}
    mobile:
      access-token-expiration: ${JWT_MOBILE_ACCESS_EXPIRATION:1800000}
    web:
      access-token-expiration: ${JWT_WEB_ACCESS_EXPIRATION:900000}

# Password Recovery Configuration
password-recovery:
  code:
    expiration-minutes: 60  # Tiempo de expiración en minutos (default: 60)


# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    defaults:
      enabled: true
    diskspace:
      enabled: true
    db:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
  metrics:
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# Application info
info:
  app:
    name: Authentication Service
    description: Spring Security Authentication and Authorization Service
    version: "@project.version@"
    encoding: "@project.build.sourceEncoding@"
    java:
      version: "@java.version@"

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
    try-it-out-enabled: true
    filter: true
    display-request-duration: true
    default-models-expand-depth: 1
    default-model-expand-depth: 1