<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- Include Spring Boot's base configuration -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- ================================================================ -->
    <!-- PERFIL DEFAULT - Configuración básica con diagnóstico          -->
    <!-- ================================================================ -->
    <springProfile name="default">
        <property name="LOG_PATH" value="logs"/>
        <property name="LOG_FILE" value="gedsys-auth"/>
        <property name="LOG_LEVEL_ROOT" value="${LOG_LEVEL_ROOT:-INFO}"/>
        <property name="LOG_LEVEL_APP" value="${LOG_LEVEL_APP:-INFO}"/>
        <property name="MAX_FILE_SIZE" value="50MB"/>
        <property name="MAX_HISTORY" value="15"/>
        
        <!-- Patrones con MDC para trazabilidad -->
        <property name="CONSOLE_PATTERN" value="%d{HH:mm:ss.SSS} [%X{userId:-}] [%X{requestId:-}] %-5level %logger{36} - %msg%n"/>
        <property name="FILE_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{userId:-}] [%X{requestId:-}] [%thread] %-5level %logger{36} - %msg%n"/>
        
        <!-- Console Appender básico -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        
        <!-- File Appender principal -->
        <appender name="FILE_APP" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/${LOG_FILE}.log</file>
            <encoder>
                <pattern>${FILE_PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/archived/${LOG_FILE}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>2GB</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
        </appender>
        
        <!-- Error Appender separado para diagnóstico -->
        <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/${LOG_FILE}-error.log</file>
            <encoder>
                <pattern>${FILE_PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/archived/${LOG_FILE}-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                <maxFileSize>20MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>500MB</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>ERROR</level>
            </filter>
        </appender>
    </springProfile>
    
    <!-- ================================================================ -->
    <!-- PERFIL DEV - Desarrollo con debugging completo                 -->
    <!-- ================================================================ -->
    <springProfile name="dev">
        <property name="LOG_PATH" value="logs"/>
        <property name="LOG_FILE" value="gedsys-auth-dev"/>
        <property name="LOG_LEVEL_ROOT" value="INFO"/>
        <property name="LOG_LEVEL_APP" value="DEBUG"/>
        <property name="MAX_FILE_SIZE" value="50MB"/>
        <property name="MAX_HISTORY" value="7"/>
        
        <!-- Patrones con colores y MDC -->
        <property name="CONSOLE_PATTERN" value="%clr(%d{HH:mm:ss.SSS}){faint} %clr([%X{userId:-}]){blue} %clr([%X{requestId:-}]){magenta} %clr(%-5level){highlight} %clr(%logger{36}){cyan} %clr(:){faint} %msg%n%throwable"/>
        <property name="FILE_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{userId:-}] [%X{requestId:-}] [%thread] %-5level [%logger{36}:%line] - %msg%n"/>
        
        <!-- Console colorido para desarrollo -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        
        <!-- File Appender con debugging detallado -->
        <appender name="FILE_APP" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/${LOG_FILE}.log</file>
            <encoder>
                <pattern>${FILE_PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/archived/${LOG_FILE}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>1GB</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
        </appender>
    </springProfile>
    
    <!-- ================================================================ -->
    <!-- PERFIL PROD - Optimizado para performance y observabilidad     -->
    <!-- ================================================================ -->
    <springProfile name="prod">
        <property name="LOG_PATH" value="logs"/>
        <property name="LOG_FILE" value="gedsys-auth-app"/>
        <property name="LOG_LEVEL_ROOT" value="WARN"/>
        <property name="LOG_LEVEL_APP" value="INFO"/>
        <property name="MAX_FILE_SIZE" value="100MB"/>
        <property name="MAX_HISTORY" value="30"/>
        
        <!-- Console Appender para producción -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        
        <!-- FILE APPENDER con JSON structured logging -->
        <appender name="FILE_JSON" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/${LOG_FILE}.json</file>
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <includeMdc>true</includeMdc>
                <includeContext>true</includeContext>
                <includeCallerData>false</includeCallerData>
                <customFields>{"service":"gedsys-auth","environment":"prod","version":"${project.version:1.0.0}"}</customFields>
                <fieldNames>
                    <timestamp>@timestamp</timestamp>
                    <level>level</level>
                    <thread>thread</thread>
                    <logger>logger</logger>
                    <message>message</message>
                </fieldNames>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/archived/${LOG_FILE}.%d{yyyy-MM-dd}.%i.json.gz</fileNamePattern>
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>5GB</totalSizeCap>
                <cleanHistoryOnStart>true</cleanHistoryOnStart>
            </rollingPolicy>
        </appender>
        
        <!-- ASYNC APPENDER optimizado para producción -->
        <appender name="ASYNC_JSON" class="ch.qos.logback.classic.AsyncAppender">
            <appender-ref ref="FILE_JSON"/>
            <queueSize>2048</queueSize>
            <maxFlushTime>5000</maxFlushTime>
            <neverBlock>true</neverBlock>
            <discardingThreshold>20</discardingThreshold>
            <includeCallerData>false</includeCallerData>
        </appender>
    </springProfile>
    
    <!-- ================================================================ -->
    <!-- LOGGERS ESPECÍFICOS POR PERFIL                                 -->
    <!-- ================================================================ -->
    
    <!-- Application logger - DEFAULT -->
    <springProfile name="default">
        <logger name="co.com.gedsys.authentication" level="${LOG_LEVEL_APP}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
            <appender-ref ref="FILE_ERROR"/>
        </logger>
    </springProfile>
    
    <!-- Application logger - DEV -->
    <springProfile name="dev">
        <logger name="co.com.gedsys.authentication" level="${LOG_LEVEL_APP}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
        </logger>
        
        <!-- SQL logging para debugging -->
        <logger name="org.hibernate.SQL" level="DEBUG" additivity="false">
            <appender-ref ref="FILE_APP"/>
        </logger>
        <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" additivity="false">
            <appender-ref ref="FILE_APP"/>
        </logger>
        
        <!-- Flyway detallado -->
        <logger name="org.flywaydb" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
        </logger>
    </springProfile>
    
    <!-- Application logger - PROD -->
    <springProfile name="prod">
        <logger name="co.com.gedsys.authentication" level="${LOG_LEVEL_APP}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_JSON"/>
        </logger>
        
        <!-- Flyway en producción -->
        <logger name="org.flywaydb" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_JSON"/>
        </logger>
    </springProfile>
    
    <!-- ================================================================ -->
    <!-- LOGGERS DE FRAMEWORK - OPTIMIZADOS                             -->
    <!-- ================================================================ -->
    
    <!-- Spring Security -->
    <springProfile name="dev">
        <logger name="org.springframework.security" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
        </logger>
    </springProfile>
    
    <springProfile name="default">
        <logger name="org.springframework.security" level="WARN" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
            <appender-ref ref="FILE_ERROR"/>
        </logger>
    </springProfile>
    
    <springProfile name="prod">
        <logger name="org.springframework.security" level="WARN" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_JSON"/>
        </logger>
    </springProfile>
    
    <!-- Reducir ruido de librerías third-party -->
    <logger name="org.apache.tomcat" level="WARN"/>
    <logger name="org.apache.catalina" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    <logger name="org.springframework.boot.actuate" level="INFO"/>
    
    <!-- ================================================================ -->
    <!-- ROOT LOGGER CONFIGURATION                                      -->
    <!-- ================================================================ -->
    
    <springProfile name="default">
        <root level="${LOG_LEVEL_ROOT}">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
            <appender-ref ref="FILE_ERROR"/>
        </root>
    </springProfile>
    
    <springProfile name="dev">
        <root level="${LOG_LEVEL_ROOT}">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE_APP"/>
        </root>
    </springProfile>
    
    <springProfile name="prod">
        <root level="${LOG_LEVEL_ROOT}">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_JSON"/>
        </root>
    </springProfile>
    
</configuration>