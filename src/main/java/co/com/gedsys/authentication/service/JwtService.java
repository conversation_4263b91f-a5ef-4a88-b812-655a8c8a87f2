package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * Service for JWT token generation, validation, and claim extraction.
 * Handles different expiration times based on session type (mobile/web).
 */
@Service
public class JwtService {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtService.class);
    
    private final JwtProperties jwtProperties;
    private final SecretKey secretKey;
    
    public JwtService(JwtProperties jwtProperties) {
        this.jwtProperties = jwtProperties;
        this.secretKey = Keys.hmacShaKeyFor(jwtProperties.getSecret().getBytes());
    }
    
    /**
     * Generate JWT access token for a user with session type and session ID
     */
    public String generateAccessToken(User user, SessionType sessionType, String sessionId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("email", user.getEmail());
        claims.put("role", user.getRole().name());
        claims.put("sessionType", sessionType.name());
        claims.put("sessionId", sessionId);

        long expiration = getAccessTokenExpiration(sessionType);

        return createToken(claims, user.getEmail(), expiration);
    }

    /**
     * Generate JWT access token for a user with session type (legacy method)
     * @deprecated Use generateAccessToken(User, SessionType, String) instead
     */
    @Deprecated
    public String generateAccessToken(User user, SessionType sessionType) {
        return generateAccessToken(user, sessionType, java.util.UUID.randomUUID().toString());
    }
    
    /**
     * Generate refresh token for a user with session type
     */
    public String generateRefreshToken(User user, SessionType sessionType) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("sessionType", sessionType.name());
        claims.put("tokenType", "refresh");
        
        return createToken(claims, user.getEmail(), jwtProperties.getRefreshTokenExpiration());
    }
    
    /**
     * Extract username (email) from token
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }
    
    /**
     * Extract user ID from token
     */
    public Long extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", Long.class));
    }
    
    /**
     * Extract role from token
     */
    public String extractRole(String token) {
        return extractClaim(token, claims -> claims.get("role", String.class));
    }
    
    /**
     * Extract session type from token
     */
    public SessionType extractSessionType(String token) {
        String sessionTypeStr = extractClaim(token, claims -> claims.get("sessionType", String.class));
        return SessionType.valueOf(sessionTypeStr);
    }

    /**
     * Extract session ID from token
     */
    public String extractSessionId(String token) {
        return extractClaim(token, claims -> claims.get("sessionId", String.class));
    }

    /**
     * Extract expiration date from token
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }
    
    /**
     * Extract a specific claim from token
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }
    
    /**
     * Validate token against user details
     */
    public boolean validateToken(String token, User user) {
        try {
            final String username = extractUsername(token);
            final Long userId = extractUserId(token);
            
            return username.equals(user.getEmail()) 
                && userId.equals(user.getId())
                && !isTokenExpired(token)
                && user.isEnabled();
        } catch (Exception e) {
            logger.debug("Token validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if token is expired
     */
    public boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            logger.debug("Error checking token expiration: {}", e.getMessage());
            return true;
        }
    }
    
    /**
     * Validate token structure and signature without user validation
     */
    public boolean isTokenValid(String token) {
        try {
            extractAllClaims(token);
            return !isTokenExpired(token);
        } catch (Exception e) {
            logger.debug("Token structure validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if token is a refresh token
     */
    public boolean isRefreshToken(String token) {
        try {
            String tokenType = extractClaim(token, claims -> claims.get("tokenType", String.class));
            return "refresh".equals(tokenType);
        } catch (Exception e) {
            logger.debug("Error checking token type: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Get remaining time until token expiration in seconds
     */
    public long getTokenExpirationTime(String token) {
        try {
            Date expiration = extractExpiration(token);
            long currentTime = System.currentTimeMillis();
            long expirationTime = expiration.getTime();
            
            return Math.max(0, (expirationTime - currentTime) / 1000);
        } catch (Exception e) {
            logger.debug("Error getting token expiration time: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * Create JWT token with claims and expiration
     */
    private String createToken(Map<String, Object> claims, String subject, long expiration) {
        Instant now = Instant.now();
        Instant expirationInstant = now.plus(expiration, ChronoUnit.MILLIS);
        
        return Jwts.builder()
                .claims(claims)
                .subject(subject)
                .issuedAt(Date.from(now))
                .expiration(Date.from(expirationInstant))
                .signWith(secretKey, Jwts.SIG.HS256)
                .compact();
    }
    
    /**
     * Extract all claims from token
     */
    private Claims extractAllClaims(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(secretKey)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (ExpiredJwtException e) {
            logger.debug("JWT token is expired: {}", e.getMessage());
            throw e;
        } catch (UnsupportedJwtException e) {
            logger.debug("JWT token is unsupported: {}", e.getMessage());
            throw e;
        } catch (MalformedJwtException e) {
            logger.debug("JWT token is malformed: {}", e.getMessage());
            throw e;
        } catch (SecurityException e) {
            logger.debug("JWT signature validation failed: {}", e.getMessage());
            throw e;
        } catch (IllegalArgumentException e) {
            logger.debug("JWT token compact of handler are invalid: {}", e.getMessage());
            throw e;
        }
    }
    
    /**
     * Get access token expiration time based on session type
     */
    private long getAccessTokenExpiration(SessionType sessionType) {
        return switch (sessionType) {
            case MOBILE -> jwtProperties.getMobile().getAccessTokenExpiration();
            case WEB -> jwtProperties.getWeb().getAccessTokenExpiration();
        };
    }
}