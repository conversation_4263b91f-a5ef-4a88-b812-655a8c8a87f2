package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.util.SecurityConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;

import static co.com.gedsys.authentication.entity.UserStatus.DELETED;

/**
 * Custom UserDetailsService implementation for Spring Security.
 * Loads user details from the database for authentication.
 * Replaces Spring Boot's default user creation.
 */
@Service
@Transactional(readOnly = true)
public class UserDetailsServiceImpl implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    private final UserRepository userRepository;

    public UserDetailsServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * Load user by username or email for authentication.
     * This method is called by Spring Security during login.
     * The presence of this log message indicates our custom UserDetailsService is being used
     * instead of Spring Boot's default user creation.
     *
     * @param identifier the username or email
     * @return UserDetails for Spring Security
     * @throws UsernameNotFoundException if user not found or not active
     */
    @Override
    public UserDetails loadUserByUsername(String identifier) throws UsernameNotFoundException {
        logger.info(SecurityConstants.SECURITY_EVENT_USER_DETAILS_SERVICE_ACTIVE);
        logger.debug(SecurityConstants.SECURITY_EVENT_LOADING_USER_DETAILS, identifier);

        User user = findUserByIdentifier(identifier);

        logger.debug(SecurityConstants.SECURITY_EVENT_USER_FOUND,
                    user.getId(), user.getStatus(), user.isEnabled(), user.getRole());

        validateUserStatus(user);

        validateUserEnabled(user);

        validateUserActive(user);

        logger.debug(SecurityConstants.SECURITY_EVENT_USER_DETAILS_LOADED,
                    identifier, user.getId());

        return new CustomUserDetails(user);
    }

    /**
     * Validates that the user is not deleted.
     *
     * @param user the user to validate
     * @throws UsernameNotFoundException if user is deleted
     */
    private void validateUserStatus(User user) throws UsernameNotFoundException {
        if (user.getStatus() == DELETED) {
            logger.debug(SecurityConstants.SECURITY_EVENT_USER_DELETED, user.getId());
            logAuthenticationFailure();
            throw new UsernameNotFoundException(SecurityConstants.INVALID_CREDENTIALS);
        }
    }

    /**
     * Validates that the user is enabled (legacy check).
     *
     * @param user the user to validate
     * @throws UsernameNotFoundException if user is not enabled
     */
    private void validateUserEnabled(User user) throws UsernameNotFoundException {
        if (!user.isEnabled()) {
            logger.debug(SecurityConstants.SECURITY_EVENT_USER_DISABLED, user.getId());
            logAuthenticationFailure();
            throw new UsernameNotFoundException(SecurityConstants.INVALID_CREDENTIALS);
        }
    }

    /**
     * Validates that the user is active (new status check).
     *
     * @param user the user to validate
     * @throws UsernameNotFoundException if user is not active
     */
    private void validateUserActive(User user) throws UsernameNotFoundException {
        if (!user.isActive()) {
            logger.debug(SecurityConstants.SECURITY_EVENT_USER_INACTIVE,
                        user.getStatus(), user.getId());
            logAuthenticationFailure();
            throw new UsernameNotFoundException(SecurityConstants.INVALID_CREDENTIALS);
        }
    }

    /**
     * Finds a user by username or email identifier.
     *
     * @param identifier the username or email
     * @return the found user
     * @throws UsernameNotFoundException if user not found
     */
    private User findUserByIdentifier(String identifier) throws UsernameNotFoundException {
        return userRepository.findByUsernameOrEmail(identifier)
                .orElseThrow(() -> {
                    logger.debug(SecurityConstants.SECURITY_EVENT_USER_NOT_FOUND, identifier);
                    logAuthenticationFailure();
                    return new UsernameNotFoundException(SecurityConstants.INVALID_CREDENTIALS);
                });
    }

    /**
     * Logs authentication failure with consistent warning message.
     * Consolidates duplicate logging patterns across validation methods.
     */
    private void logAuthenticationFailure() {
        logger.warn(SecurityConstants.SECURITY_EVENT_AUTHENTICATION_FAILED_INVALID_CREDENTIALS);
    }

    /**
     * Custom UserDetails implementation that wraps our User entity.
     * Provides user details to Spring Security framework.
     * Implements Serializable for session management compatibility.
     */
    public static class CustomUserDetails implements UserDetails, Serializable {

        private static final long serialVersionUID = 1L;

        private final User user;

        public CustomUserDetails(User user) {
            this.user = user;
        }

        public User getUser() {
            return user;
        }

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            return Collections.singletonList(
                new SimpleGrantedAuthority(SecurityConstants.ROLE_PREFIX + user.getRole().name())
            );
        }

        @Override
        public String getPassword() {
            return user.getPassword();
        }

        @Override
        public String getUsername() {
            return user.getUsername();
        }

        @Override
        public boolean isAccountNonExpired() {
            return true; // Account expiration not implemented
        }

        @Override
        public boolean isAccountNonLocked() {
            return true; // Account locking not implemented
        }

        @Override
        public boolean isCredentialsNonExpired() {
            return true; // Credential expiration not implemented
        }

        @Override
        public boolean isEnabled() {
            return user.isEnabled() && user.isActive();
        }

        @Override
        public String toString() {
            return "CustomUserDetails{" +
                   "userId=" + user.getId() +
                   ", username='" + user.getUsername() + '\'' +
                   ", email='" + user.getEmail() + '\'' +
                   ", role=" + user.getRole() +
                   ", status=" + user.getStatus() +
                   ", enabled=" + user.isEnabled() +
                   '}';
        }
    }
}