package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.Normalizer;
import java.util.regex.Pattern;

/**
 * Component responsible for generating unique usernames based on user's first and last names.
 * Implements the algorithm: first initial + last name, with incremental numbers for duplicates.
 */
@Component
public class UsernameGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(UsernameGenerator.class);
    private static final Pattern DIACRITICS_PATTERN = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
    private static final Pattern NON_ALPHANUMERIC_PATTERN = Pattern.compile("[^a-zA-Z0-9]");
    
    /**
     * Generates a unique username based on the user's first and last names.
     * Algorithm:
     * 1. Take first initial of firstName in lowercase
     * 2. Concatenate with full lastName in lowercase
     * 3. Remove spaces and special characters
     * 4. Check uniqueness against all users (including deleted ones)
     * 5. If exists, add incremental number (2, 3, 4...)
     * 
     * @param firstName the user's first name
     * @param lastName the user's last name
     * @param userRepository repository to check for existing usernames
     * @return a unique username
     * @throws IllegalArgumentException if firstName or lastName is null or empty
     */
    public String generateUsername(String firstName, String lastName, UserRepository userRepository) {
        logger.debug("Generating username for firstName: {} lastName: {}", firstName, lastName);
        
        if (firstName == null || firstName.trim().isEmpty()) {
            throw new IllegalArgumentException("First name cannot be null or empty");
        }
        
        if (lastName == null || lastName.trim().isEmpty()) {
            throw new IllegalArgumentException("Last name cannot be null or empty");
        }
        
        // Generate base username
        String baseUsername = generateBaseUsername(firstName, lastName);
        logger.debug("Generated base username: {}", baseUsername);
        
        // Find available username (checking for duplicates including deleted users)
        String finalUsername = findAvailableUsername(baseUsername, userRepository);
        logger.info("Generated unique username: {}", finalUsername);
        
        return finalUsername;
    }
    
    /**
     * Generates the base username from first and last names.
     * Takes first initial of firstName + full lastName, sanitized.
     * 
     * @param firstName the user's first name
     * @param lastName the user's last name
     * @return the base username (without uniqueness check)
     */
    private String generateBaseUsername(String firstName, String lastName) {
        // Get first initial
        String firstInitial = firstName.trim().substring(0, 1).toLowerCase();
        
        // Get full last name
        String fullLastName = lastName.trim().toLowerCase();
        
        // Combine and sanitize
        String combined = firstInitial + fullLastName;
        return sanitizeString(combined);
    }
    
    /**
     * Sanitizes a string by removing diacritics, spaces, and special characters.
     * Keeps only alphanumeric characters.
     * 
     * @param input the string to sanitize
     * @return sanitized string containing only alphanumeric characters
     */
    private String sanitizeString(String input) {
        if (input == null) {
            return "";
        }
        
        // Remove diacritics (accents)
        String normalized = Normalizer.normalize(input, Normalizer.Form.NFD);
        String withoutDiacritics = DIACRITICS_PATTERN.matcher(normalized).replaceAll("");
        
        // Remove non-alphanumeric characters
        String alphanumericOnly = NON_ALPHANUMERIC_PATTERN.matcher(withoutDiacritics).replaceAll("");
        
        return alphanumericOnly.toLowerCase();
    }
    
    /**
     * Finds an available username by checking against existing users.
     * If the base username exists, adds incremental numbers (2, 3, 4...).
     * Checks against ALL users including deleted ones to avoid confusion.
     * 
     * @param baseUsername the base username to check
     * @param userRepository repository to check for existing usernames
     * @return an available username
     */
    private String findAvailableUsername(String baseUsername, UserRepository userRepository) {
        String candidateUsername = baseUsername;
        int counter = 2;
        
        // Check if base username is available
        while (userRepository.existsByUsernameIncludingDeleted(candidateUsername)) {
            candidateUsername = baseUsername + counter;
            counter++;
            logger.debug("Username {} exists, trying: {}", baseUsername, candidateUsername);
        }
        
        return candidateUsername;
    }
}
