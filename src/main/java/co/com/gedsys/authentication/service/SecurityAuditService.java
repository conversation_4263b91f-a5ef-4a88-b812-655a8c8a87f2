package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.SecurityAuditLog;
import co.com.gedsys.authentication.entity.SecurityEventType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.exception.SecurityAuditException;
import co.com.gedsys.authentication.repository.SecurityAuditLogRepository;
import co.com.gedsys.authentication.util.HttpRequestContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@Transactional
public class SecurityAuditService {

    private static final Logger logger = LoggerFactory.getLogger(SecurityAuditService.class);
    private static final String USER_ID_KEY = "userId";

    private final SecurityAuditLogRepository auditLogRepository;

    public SecurityAuditService(SecurityAuditLogRepository auditLogRepository) {
        this.auditLogRepository = auditLogRepository;
    }

    public SecurityAuditLog logSecurityEvent(User user, SecurityEventType eventType, String description) {
        return logSecurityEvent(user, eventType, description, null);
    }

    public SecurityAuditLog logSecurityEvent(User user, SecurityEventType eventType, String description, 
                                           Map<String, Object> details) {
        try {
            SecurityAuditLog auditLog = new SecurityAuditLog();
            auditLog.setUser(user);
            auditLog.setEventType(eventType);
            auditLog.setEventDescription(description);
            auditLog.setEventDetails(details);
            
            setHttpRequestInfo(auditLog);
            
            auditLog.setCreatedAt(LocalDateTime.now());
            
            SecurityAuditLog savedLog = auditLogRepository.save(auditLog);
            
            logger.info("Evento de seguridad registrado: {} para usuario: {} (ID: {})", 
                       eventType, user != null ? user.getEmail() : "N/A", user != null ? user.getId() : "N/A");
            
            return savedLog;
            
        } catch (Exception e) {
            throw new SecurityAuditException("Error al registrar evento de auditoría", e);
        }
    }

    private void setHttpRequestInfo(SecurityAuditLog auditLog) {
        try {
            auditLog.setIpAddress(HttpRequestContextUtil.getClientIpAddress());
            auditLog.setUserAgent(HttpRequestContextUtil.getUserAgent());
        } catch (Exception e) {
            logger.warn("No se pudo obtener información de la request HTTP: {}", e.getMessage(), e);
        }
    }

    public void logPasswordRecoveryRequest(User user, String identifier) {
        Map<String, Object> details = new HashMap<>();
        details.put("identifier", identifier);
        details.put(USER_ID_KEY, user.getId());
        details.put("userEmail", user.getEmail());
        
        logSecurityEvent(user, SecurityEventType.PASSWORD_RECOVERY_REQUESTED, 
                        "Solicitud de recuperación de contraseña iniciada", details);
    }

    public void logPasswordRecoveryCodeGenerated(User user, String codeId) {
        Map<String, Object> details = new HashMap<>();
        details.put("codeId", codeId);
        details.put(USER_ID_KEY, user.getId());
        
        logSecurityEvent(user, SecurityEventType.PASSWORD_RECOVERY_CODE_GENERATED, 
                        "Código de recuperación generado", details);
    }

    public void logPasswordRecoverySuccess(User user, String codeUsed) {
        Map<String, Object> details = new HashMap<>();
        details.put("codeUsed", codeUsed);
        details.put(USER_ID_KEY, user.getId());
        
        logSecurityEvent(user, SecurityEventType.PASSWORD_RECOVERY_SUCCESS, 
                        "Contraseña recuperada exitosamente", details);
    }

    public void logPasswordRecoveryFailed(User user, String reason, String codeAttempted) {
        Map<String, Object> details = new HashMap<>();
        details.put("reason", reason);
        details.put("codeAttempted", codeAttempted != null ? "***" : null);
        details.put(USER_ID_KEY, user != null ? user.getId() : null);
        
        logSecurityEvent(user, SecurityEventType.PASSWORD_RECOVERY_FAILED, 
                        "Intento fallido de recuperación de contraseña: " + reason, details);
    }

    public void logPasswordRecoveryExpired(User user, String codeExpired) {
        Map<String, Object> details = new HashMap<>();
        details.put("codeExpired", codeExpired);
        details.put(USER_ID_KEY, user.getId());
        
        logSecurityEvent(user, SecurityEventType.PASSWORD_RECOVERY_EXPIRED, 
                        "Código de recuperación expirado", details);
    }

    public void logPasswordRecoveryRateLimited(String identifier) {
        Map<String, Object> details = new HashMap<>();
        details.put("identifier", identifier);
        
        logSecurityEvent(null, SecurityEventType.PASSWORD_RECOVERY_RATE_LIMITED, 
                        "Límite de intentos de recuperación excedido para " + identifier, details);
    }

    @Transactional(readOnly = true)
    public long countRecentFailedAttempts(User user, int minutes) {
        LocalDateTime since = LocalDateTime.now().minusMinutes(minutes);
        return auditLogRepository.countByUserAndEventTypeAndCreatedAtAfter(
                user, SecurityEventType.PASSWORD_RECOVERY_FAILED, since);
    }
}