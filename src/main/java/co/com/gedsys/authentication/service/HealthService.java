package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Service
public class HealthService {

    private static final Logger logger = LoggerFactory.getLogger(HealthService.class);

    private static final String KEY_STATUS = "status";
    private static final String KEY_ERROR = "error";
    private static final String KEY_DATABASE = "database";
    private static final String KEY_JWT = "jwt";
    private static final String KEY_TIMESTAMP = "timestamp";
    private static final String VAL_UP = "UP";
    private static final String VAL_DOWN = "DOWN";

    private final DataSource dataSource;
    private final JwtProperties jwtProperties;
    private final UserRepository userRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PushTokenRepository pushTokenRepository;

    public HealthService(DataSource dataSource,
                        JwtProperties jwtProperties,
                        UserRepository userRepository,
                        RefreshTokenRepository refreshTokenRepository,
                        PushTokenRepository pushTokenRepository) {
        this.dataSource = dataSource;
        this.jwtProperties = jwtProperties;
        this.userRepository = userRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.pushTokenRepository = pushTokenRepository;
    }

    public Map<String, Object> getBasicHealth() {
        Map<String, Object> health = new HashMap<>();

        try {
            // Check database connectivity
            boolean dbHealthy = checkDatabaseHealth();
            health.put(KEY_DATABASE, dbHealthy ? VAL_UP : VAL_DOWN);

            // Check JWT configuration
            boolean jwtHealthy = checkJwtHealth();
            health.put(KEY_JWT, jwtHealthy ? VAL_UP : VAL_DOWN);

            // Overall status
            boolean overallHealthy = dbHealthy && jwtHealthy;
            health.put(KEY_STATUS, overallHealthy ? VAL_UP : VAL_DOWN);
            health.put(KEY_TIMESTAMP, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            logger.debug("Health check completed - Database: {}, JWT: {}", dbHealthy, jwtHealthy);

        } catch (Exception e) {
            logger.error("Health check failed", e);
            health.put(KEY_STATUS, VAL_DOWN);
            health.put(KEY_ERROR, e.getMessage());
            health.put(KEY_TIMESTAMP, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }

        return health;
    }

    public Map<String, Object> getDetailedHealth() {
        Map<String, Object> health = new HashMap<>();

        try {
            // Database health with details
            Map<String, Object> dbHealth = getDetailedDatabaseHealth();
            health.put(KEY_DATABASE, dbHealth);

            // JWT health with details
            Map<String, Object> jwtHealth = getDetailedJwtHealth();
            health.put(KEY_JWT, jwtHealth);

            // Application metrics
            Map<String, Object> metrics = getApplicationMetrics();
            health.put("metrics", metrics);

            // Overall status
            boolean dbHealthy = VAL_UP.equals(dbHealth.get(KEY_STATUS));
            boolean jwtHealthy = VAL_UP.equals(jwtHealth.get(KEY_STATUS));
            boolean overallHealthy = dbHealthy && jwtHealthy;

            health.put(KEY_STATUS, overallHealthy ? VAL_UP : VAL_DOWN);
            health.put(KEY_TIMESTAMP, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            logger.debug("Detailed health check completed");

        } catch (Exception e) {
            logger.error("Detailed health check failed", e);
            health.put(KEY_STATUS, VAL_DOWN);
            health.put(KEY_ERROR, e.getMessage());
            health.put(KEY_TIMESTAMP, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }

        return health;
    }

    private boolean checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            return !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            logger.warn("Database health check failed", e);
            return false;
        }
    }

    private boolean checkJwtHealth() {
        try {
            // Check if JWT secret is configured
            String secret = jwtProperties.getSecret();
            if (secret == null || secret.trim().isEmpty()) {
                return false;
            }

            // Check if expiration times are valid
            long accessTokenExpiration = jwtProperties.getAccessTokenExpiration();
            long refreshTokenExpiration = jwtProperties.getRefreshTokenExpiration();

            return accessTokenExpiration > 0 && refreshTokenExpiration > 0;

        } catch (Exception e) {
            logger.warn("JWT health check failed", e);
            return false;
        }
    }

    private Map<String, Object> getDetailedDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();

        try (Connection connection = dataSource.getConnection()) {
            if (connection.isClosed() || !connection.isValid(5)) {
                dbHealth.put(KEY_STATUS, VAL_DOWN);
                dbHealth.put(KEY_ERROR, "Database connection is not valid");
                return dbHealth;
            }

            dbHealth.put(KEY_STATUS, VAL_UP);
            dbHealth.put(KEY_DATABASE, connection.getMetaData().getDatabaseProductName());
            dbHealth.put("version", connection.getMetaData().getDatabaseProductVersion());

            // Check table existence using repositories instead of SQL
            dbHealth.put("tables", checkTablesExist());

        } catch (SQLException e) {
            logger.warn("Detailed database health check failed", e);
            dbHealth.put(KEY_STATUS, VAL_DOWN);
            dbHealth.put(KEY_ERROR, e.getMessage());
        }

        return dbHealth;
    }

    private Map<String, Object> getDetailedJwtHealth() {
        Map<String, Object> jwtHealth = new HashMap<>();

        try {
            // Check JWT configuration
            String secret = jwtProperties.getSecret();
            if (secret == null || secret.trim().isEmpty()) {
                jwtHealth.put(KEY_STATUS, VAL_DOWN);
                jwtHealth.put(KEY_ERROR, "JWT secret not configured");
                return jwtHealth;
            }

            jwtHealth.put(KEY_STATUS, VAL_UP);
            jwtHealth.put("secret_configured", true);
            jwtHealth.put("secret_length", secret.length());
            jwtHealth.put("access_token_expiration_ms", jwtProperties.getAccessTokenExpiration());
            jwtHealth.put("refresh_token_expiration_ms", jwtProperties.getRefreshTokenExpiration());

            // Check mobile and web specific settings
            if (jwtProperties.getMobile() != null) {
                jwtHealth.put("mobile_access_token_expiration_ms",
                             jwtProperties.getMobile().getAccessTokenExpiration());
            }

            if (jwtProperties.getWeb() != null) {
                jwtHealth.put("web_access_token_expiration_ms",
                             jwtProperties.getWeb().getAccessTokenExpiration());
            }

        } catch (Exception e) {
            logger.warn("Detailed JWT health check failed", e);
            jwtHealth.put(KEY_STATUS, VAL_DOWN);
            jwtHealth.put(KEY_ERROR, e.getMessage());
        }

        return jwtHealth;
    }

    private Map<String, Object> getApplicationMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        try {
            LocalDateTime now = LocalDateTime.now();

            // User metrics
            Map<String, Object> userMetrics = new HashMap<>();
            userMetrics.put("total", userRepository.count());
            userMetrics.put("enabled", userRepository.countByEnabled(true));
            userMetrics.put("disabled", userRepository.countByEnabled(false));
            userMetrics.put("admin", userRepository.countByRole(co.com.gedsys.authentication.entity.Role.ADMIN));
            metrics.put("users", userMetrics);

            // Token metrics
            Map<String, Object> tokenMetrics = new HashMap<>();
            tokenMetrics.put("refresh_active", refreshTokenRepository.countValidTokens(now));
            tokenMetrics.put("refresh_expired", refreshTokenRepository.countExpiredTokens(now));
            tokenMetrics.put("push_total", pushTokenRepository.count());

            LocalDateTime staleDate = now.minusDays(30);
            tokenMetrics.put("push_stale", pushTokenRepository.countStaleTokens(staleDate));
            metrics.put("tokens", tokenMetrics);

        } catch (Exception e) {
            logger.warn("Failed to collect application metrics", e);
            metrics.put(KEY_ERROR, "Failed to collect metrics: " + e.getMessage());
        }
        
        return metrics;
    }
    
    private Map<String, Boolean> checkTablesExist() {
        Map<String, Boolean> tables = new HashMap<>();
        
        // Check users table by attempting to count
        tables.put("users", checkRepositoryHealth(() -> userRepository.count()));
        
        // Check refresh_tokens table by attempting to count
        tables.put("refresh_tokens", checkRepositoryHealth(() -> refreshTokenRepository.count()));
        
        // Check push_tokens table by attempting to count
        tables.put("push_tokens", checkRepositoryHealth(() -> pushTokenRepository.count()));
        
        return tables;
    }
    
    private boolean checkRepositoryHealth(Runnable repositoryOperation) {
        try {
            repositoryOperation.run();
            return true;
        } catch (Exception e) {
            logger.warn("Repository health check failed", e);
            return false;
        }
    }
}