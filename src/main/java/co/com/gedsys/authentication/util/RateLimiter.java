package co.com.gedsys.authentication.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public class RateLimiter {

    private static final int MAX_ATTEMPTS = 3;
    private static final int TIME_WINDOW_MINUTES = 15;
    
    private final ConcurrentMap<String, AttemptRecord> attempts = new ConcurrentHashMap<>();

    public boolean isAllowed(String identifier) {
        cleanup();
        
        AttemptRecord attemptRecord = attempts.get(identifier);
        
        if (attemptRecord == null) {
            return true;
        }
        
        if (isRecordExpired(attemptRecord)) {
            attempts.remove(identifier);
            return true;
        }
        
        return attemptRecord.count < MAX_ATTEMPTS;
    }

    public void recordAttempt(String identifier) {
        cleanup();
        
        attempts.compute(identifier, (key, existingRecord) -> {
            if (existingRecord == null || isRecordExpired(existingRecord)) {
                return new AttemptRecord(LocalDateTime.now(), 1);
            } else {
                return new AttemptRecord(existingRecord.firstAttemptTime, existingRecord.count + 1);
            }
        });
    }

    public int getRemainingAttempts(String identifier) {
        cleanup();
        
        AttemptRecord attemptRecord = attempts.get(identifier);
        
        if (attemptRecord == null || isRecordExpired(attemptRecord)) {
            return MAX_ATTEMPTS;
        }
        
        return Math.max(0, MAX_ATTEMPTS - attemptRecord.count);
    }

    public LocalDateTime getResetTime(String identifier) {
        cleanup();
        
        AttemptRecord attemptRecord = attempts.get(identifier);
        
        if (attemptRecord == null || isRecordExpired(attemptRecord)) {
            return LocalDateTime.now();
        }
        
        return attemptRecord.firstAttemptTime.plus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
    }

    private boolean isRecordExpired(AttemptRecord attemptRecord) {
        LocalDateTime expireTime = attemptRecord.firstAttemptTime.plus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
        return LocalDateTime.now().isAfter(expireTime);
    }

    private void cleanup() {
        LocalDateTime currentTime = LocalDateTime.now();
        attempts.entrySet().removeIf(entry -> {
            AttemptRecord attemptRecord = entry.getValue();
            LocalDateTime expireTime = attemptRecord.firstAttemptTime.plus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
            return currentTime.isAfter(expireTime);
        });
    }

    private static class AttemptRecord {
        final LocalDateTime firstAttemptTime;
        final int count;

        AttemptRecord(LocalDateTime firstAttemptTime, int count) {
            this.firstAttemptTime = firstAttemptTime;
            this.count = count;
        }
    }
}