package co.com.gedsys.authentication.util;

import co.com.gedsys.authentication.exception.CodeGenerationException;
import co.com.gedsys.authentication.repository.PasswordRecoveryCodeRepository;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;

@Component
public class SecureCodeGenerator {

    private static final String ALPHANUMERIC_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CODE_LENGTH = 8;
    private static final SecureRandom random = new SecureRandom();
    
    private final PasswordRecoveryCodeRepository passwordRecoveryCodeRepository;

    public SecureCodeGenerator(PasswordRecoveryCodeRepository passwordRecoveryCodeRepository) {
        this.passwordRecoveryCodeRepository = passwordRecoveryCodeRepository;
    }

    public String generateUniqueCode() {
        String code;
        int attempts = 0;
        int maxAttempts = 10;
        
        do {
            code = generateRandomCode();
            attempts++;
            
            if (attempts > maxAttempts) {
                throw new CodeGenerationException("No se pudo generar un código único después de " + maxAttempts + " intentos");
            }
            
        } while (passwordRecoveryCodeRepository.existsByCode(code));
        
        return code;
    }

    private String generateRandomCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(ALPHANUMERIC_CHARS.length());
            code.append(ALPHANUMERIC_CHARS.charAt(index));
        }
        
        return code.toString();
    }

    public boolean isValidCodeFormat(String code) {
        if (code == null || code.length() != CODE_LENGTH) {
            return false;
        }
        
        return code.chars().allMatch(c -> ALPHANUMERIC_CHARS.indexOf(c) >= 0);
    }
}