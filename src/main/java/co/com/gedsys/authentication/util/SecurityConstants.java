package co.com.gedsys.authentication.util;

/**
 * Security-related constants for logging and error messages.
 * Centralizes security event messages to avoid duplication and improve maintainability.
 */
public final class SecurityConstants {

    // Security event log messages
    public static final String SECURITY_EVENT_USER_DETAILS_SERVICE_ACTIVE =
        "SECURITY_EVENT: Custom UserDetailsService is active - loading user details for authentication";

    public static final String SECURITY_EVENT_LOADING_USER_DETAILS =
        "SECURITY_EVENT: Loading user details for authentication - identifier: {}";

    public static final String SECURITY_EVENT_USER_NOT_FOUND =
        "SECURITY_EVENT: Authentication failed - user not found for identifier: {}";

    public static final String SECURITY_EVENT_AUTHENTICATION_FAILED_INVALID_CREDENTIALS =
        "SECURITY_EVENT: Authentication failed - invalid credentials";

    public static final String SECURITY_EVENT_USER_FOUND =
        "SECURITY_EVENT: User found - ID: {}, Status: {}, Enabled: {}, Role: {}";

    public static final String SECURITY_EVENT_USER_DELETED =
        "SECURITY_EVENT: Authentication failed - user deleted (ID: {})";

    public static final String SECURITY_EVENT_USER_DISABLED =
        "SECURITY_EVENT: Authentication failed - user disabled (ID: {})";

    public static final String SECURITY_EVENT_USER_INACTIVE =
        "SECURITY_EVENT: Authentication failed - user inactive status: {} (ID: {})";

    public static final String SECURITY_EVENT_USER_DETAILS_LOADED =
        "SECURITY_EVENT: User details loaded successfully - identifier: {} (ID: {})";

    // Error messages
    public static final String INVALID_CREDENTIALS = "Invalid credentials";

    // Role prefix
    public static final String ROLE_PREFIX = "ROLE_";

    private SecurityConstants() {
        // Utility class - prevent instantiation
    }
}