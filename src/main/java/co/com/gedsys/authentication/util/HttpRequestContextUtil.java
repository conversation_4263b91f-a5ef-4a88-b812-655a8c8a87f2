package co.com.gedsys.authentication.util;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Utility class for extracting HTTP request context information.
 * Provides methods to safely extract client IP address and user agent from HTTP requests.
 */
public class HttpRequestContextUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpRequestContextUtil.class);
    
    // Common headers for IP address detection
    private static final String[] IP_HEADER_CANDIDATES = {
        "X-Forwarded-For",
        "X-Real-IP", 
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED",
        "HTTP_X_CLUSTER_CLIENT_IP",
        "HTTP_CLIENT_IP",
        "HTTP_FORWARDED_FOR",
        "HTTP_FORWARDED",
        "HTTP_VIA",
        "REMOTE_ADDR"
    };
    
    private static final String UNKNOWN = "unknown";
    private static final String USER_AGENT_HEADER = "User-Agent";
    
    /**
     * Extract client IP address from the current HTTP request.
     * Handles various proxy headers and fallbacks to remote address.
     * 
     * @return client IP address or null if not available
     */
    public static String getClientIpAddress() {
        try {
            HttpServletRequest request = getCurrentHttpRequest();
            if (request == null) {
                logger.debug("No HTTP request available in current context");
                return null;
            }
            
            return extractClientIpAddress(request);
            
        } catch (Exception e) {
            logger.debug("Failed to extract client IP address: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract user agent from the current HTTP request.
     * 
     * @return user agent string or null if not available
     */
    public static String getUserAgent() {
        try {
            HttpServletRequest request = getCurrentHttpRequest();
            if (request == null) {
                logger.debug("No HTTP request available in current context");
                return null;
            }
            
            String userAgent = request.getHeader(USER_AGENT_HEADER);
            if (userAgent != null && !userAgent.trim().isEmpty()) {
                // Truncate very long user agent strings
                return userAgent.length() > 1000 ? userAgent.substring(0, 1000) : userAgent;
            }
            
            return null;
            
        } catch (Exception e) {
            logger.debug("Failed to extract user agent: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract client IP address from a specific HTTP request.
     * 
     * @param request the HTTP request
     * @return client IP address or null if not determinable
     */
    public static String extractClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        
        // Primary source: remote address (most reliable)
        String remoteAddr = request.getRemoteAddr();
        if (isValidIpAddress(remoteAddr)) {
            logger.debug("Client IP extracted from remote address: {}", remoteAddr);
            return remoteAddr;
        }
        
        // Fallback: check various headers for proxy scenarios
        for (String header : IP_HEADER_CANDIDATES) {
            String ip = request.getHeader(header);
            if (isValidIpAddress(ip)) {
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (isValidIpAddress(ip)) {
                    logger.debug("Client IP extracted from header {}: {}", header, ip);
                    return ip;
                }
            }
        }
        
        logger.debug("Unable to determine client IP address");
        return null;
    }
    
    /**
     * Extract user agent from a specific HTTP request.
     * 
     * @param request the HTTP request
     * @return user agent string or null if not available
     */
    public static String extractUserAgent(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        
        String userAgent = request.getHeader(USER_AGENT_HEADER);
        if (userAgent != null && !userAgent.trim().isEmpty()) {
            // Truncate very long user agent strings
            return userAgent.length() > 1000 ? userAgent.substring(0, 1000) : userAgent;
        }
        
        return null;
    }
    
    /**
     * Get the current HTTP request from the request context.
     * 
     * @return current HTTP request or null if not available
     */
    private static HttpServletRequest getCurrentHttpRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            logger.debug("Failed to get current HTTP request: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Validate if an IP address string is valid and not unknown.
     * 
     * @param ip the IP address string to validate
     * @return true if the IP address is valid
     */
    private static boolean isValidIpAddress(String ip) {
        return ip != null && 
               !ip.trim().isEmpty() && 
               !UNKNOWN.equalsIgnoreCase(ip.trim());
    }
    
    /**
     * Create a session context object with current request information.
     * 
     * @return session context with IP and user agent
     */
    public static SessionContext createSessionContext() {
        return new SessionContext(getClientIpAddress(), getUserAgent());
    }
    
    /**
     * Session context holder for request information.
     */
    public static class SessionContext {
        private final String clientIpAddress;
        private final String userAgent;
        
        public SessionContext(String clientIpAddress, String userAgent) {
            this.clientIpAddress = clientIpAddress;
            this.userAgent = userAgent;
        }
        
        public String getClientIpAddress() {
            return clientIpAddress;
        }
        
        public String getUserAgent() {
            return userAgent;
        }
        
        @Override
        public String toString() {
            return "SessionContext{" +
                    "clientIpAddress='" + clientIpAddress + '\'' +
                    ", userAgent='" + (userAgent != null && userAgent.length() > 50 ? 
                                      userAgent.substring(0, 50) + "..." : userAgent) + '\'' +
                    '}';
        }
    }
}
