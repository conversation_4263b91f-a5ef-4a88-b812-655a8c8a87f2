package co.com.gedsys.authentication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Table(name = "security_audit_log", indexes = {
    @Index(name = "idx_security_audit_log_user_id", columnList = "user_id"),
    @Index(name = "idx_security_audit_log_event_type", columnList = "event_type"),
    @Index(name = "idx_security_audit_log_created_at", columnList = "created_at"),
    @Index(name = "idx_security_audit_log_ip_address", columnList = "ip_address")
})
public class SecurityAuditLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = true)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false, length = 100)
    @NotNull(message = "Event type is required")
    private SecurityEventType eventType;

    @Column(name = "event_description", nullable = false)
    @NotBlank(message = "Event description is required")
    private String eventDescription;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_details", columnDefinition = "jsonb")
    private transient Map<String, Object> eventDetails;

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "created_at", nullable = false)
    @NotNull(message = "Created at is required")
    private LocalDateTime createdAt = LocalDateTime.now();

    public SecurityAuditLog() {}

    public SecurityAuditLog(User user, SecurityEventType eventType, String eventDescription) {
        this.user = user;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
    }

    public SecurityAuditLog(User user, SecurityEventType eventType, String eventDescription, 
                           Map<String, Object> eventDetails) {
        this(user, eventType, eventDescription);
        this.eventDetails = eventDetails;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public SecurityEventType getEventType() {
        return eventType;
    }

    public void setEventType(SecurityEventType eventType) {
        this.eventType = eventType;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public Map<String, Object> getEventDetails() {
        return eventDetails;
    }

    public void setEventDetails(Map<String, Object> eventDetails) {
        this.eventDetails = eventDetails;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SecurityAuditLog that = (SecurityAuditLog) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "SecurityAuditLog{" +
                "id=" + id +
                ", eventType=" + eventType +
                ", eventDescription='" + eventDescription + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}