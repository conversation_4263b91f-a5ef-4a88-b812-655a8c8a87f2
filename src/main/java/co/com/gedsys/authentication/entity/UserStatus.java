package co.com.gedsys.authentication.entity;

/**
 * Enum representing the status of a user in the system.
 * Supports logical deletion and user lifecycle management.
 */
public enum UserStatus {
    /**
     * User is active and can access the system.
     * Equivalent to enabled=true in the legacy system.
     */
    ACTIVE,
    
    /**
     * User is inactive but not deleted.
     * Equivalent to enabled=false in the legacy system.
     */
    INACTIVE,
    
    /**
     * User has been logically deleted.
     * User cannot access the system but data is preserved for audit purposes.
     */
    DELETED
}
