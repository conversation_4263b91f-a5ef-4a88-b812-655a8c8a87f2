package co.com.gedsys.authentication.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * Component that validates application configuration and connectivity on startup.
 * Ensures JWT configuration is valid and database connectivity is working.
 */
@Component
public class ApplicationStartupValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartupValidator.class);
    
    private final JwtProperties jwtProperties;
    private final DataSource dataSource;
    private final Environment environment;
    
    public ApplicationStartupValidator(JwtProperties jwtProperties, DataSource dataSource, Environment environment) {
        this.jwtProperties = jwtProperties;
        this.dataSource = dataSource;
        this.environment = environment;
    }
    
    /**
     * Validates application configuration after the application is ready.
     * This method is called when the ApplicationReadyEvent is fired.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateApplicationStartup() {
        logger.info("STARTUP_VALIDATION: Starting application configuration validation");

        // Check for default Spring Boot user creation
        checkForDefaultUserCreation();

        boolean jwtValid = validateJwtConfiguration();
        boolean dbValid = validateDatabaseConnectivity();

        if (jwtValid && dbValid) {
            logger.info("STARTUP_VALIDATION: Application startup validation completed successfully");
        } else {
            logger.error("STARTUP_VALIDATION: Application startup validation failed - JWT valid: {}, DB valid: {}",
                        jwtValid, dbValid);
        }
    }

    /**
     * Check if Spring Boot is creating a default user (security risk).
     * This indicates missing UserDetailsService implementation.
     */
    private void checkForDefaultUserCreation() {
        logger.info("STARTUP_VALIDATION: Checking for default Spring Boot user creation...");

        // Check if default user properties are configured in application properties
        String defaultUsername = environment.getProperty("spring.security.user.name");
        String defaultPassword = environment.getProperty("spring.security.user.password");
        String defaultRoles = environment.getProperty("spring.security.user.roles");

        if (defaultUsername != null || defaultPassword != null || defaultRoles != null) {
            logger.error("STARTUP_VALIDATION: SECURITY RISK DETECTED - Spring Boot default user properties configured!");
            logger.error("STARTUP_VALIDATION: This indicates missing custom UserDetailsService implementation");
            logger.error("STARTUP_VALIDATION: Default user properties should be removed in production environments");
            
            // Log presence of properties without exposing values
            if (defaultUsername != null) {
                logger.error("STARTUP_VALIDATION: spring.security.user.name is configured");
            }
            if (defaultPassword != null) {
                logger.error("STARTUP_VALIDATION: spring.security.user.password is configured");
            }
            if (defaultRoles != null) {
                logger.error("STARTUP_VALIDATION: spring.security.user.roles is configured");
            }
        } else {
            logger.info("STARTUP_VALIDATION: No default Spring Boot user properties detected - security configuration is correct");
        }
    }
    
    /**
     * Validates JWT configuration properties.
     * Requirement 7.4: Use default values when configuration is missing
     * Requirement 7.5: Log errors when configuration is invalid and use defaults
     * 
     * @return true if JWT configuration is valid or defaults are applied
     */
    private boolean validateJwtConfiguration() {
        logger.debug("STARTUP_VALIDATION: Validating JWT configuration");
        
        boolean isValid = true;
        
        // Validate JWT secret
        if (jwtProperties.getSecret() == null || jwtProperties.getSecret().trim().isEmpty()) {
            logger.error("STARTUP_VALIDATION: JWT secret is not configured - using default value");
            isValid = false;
        } else if (jwtProperties.getSecret().length() < 32) {
            logger.warn("STARTUP_VALIDATION: JWT secret is too short (< 32 characters) - security risk detected");
        }
        
        // Validate access token expiration
        if (jwtProperties.getAccessTokenExpiration() <= 0) {
            logger.error("STARTUP_VALIDATION: Invalid JWT access token expiration: {} - using default value", 
                        jwtProperties.getAccessTokenExpiration());
            isValid = false;
        } else {
            logger.debug("STARTUP_VALIDATION: JWT access token expiration: {} ms", 
                        jwtProperties.getAccessTokenExpiration());
        }
        
        // Validate refresh token expiration
        if (jwtProperties.getRefreshTokenExpiration() <= 0) {
            logger.error("STARTUP_VALIDATION: Invalid JWT refresh token expiration: {} - using default value", 
                        jwtProperties.getRefreshTokenExpiration());
            isValid = false;
        } else {
            logger.debug("STARTUP_VALIDATION: JWT refresh token expiration: {} ms", 
                        jwtProperties.getRefreshTokenExpiration());
        }
        
        // Validate mobile access token expiration
        if (jwtProperties.getMobile().getAccessTokenExpiration() <= 0) {
            logger.error("STARTUP_VALIDATION: Invalid mobile JWT access token expiration: {} - using default value", 
                        jwtProperties.getMobile().getAccessTokenExpiration());
            isValid = false;
        } else {
            logger.debug("STARTUP_VALIDATION: Mobile JWT access token expiration: {} ms", 
                        jwtProperties.getMobile().getAccessTokenExpiration());
        }
        
        // Validate web access token expiration
        if (jwtProperties.getWeb().getAccessTokenExpiration() <= 0) {
            logger.error("STARTUP_VALIDATION: Invalid web JWT access token expiration: {} - using default value", 
                        jwtProperties.getWeb().getAccessTokenExpiration());
            isValid = false;
        } else {
            logger.debug("STARTUP_VALIDATION: Web JWT access token expiration: {} ms", 
                        jwtProperties.getWeb().getAccessTokenExpiration());
        }
        
        // Validate that refresh token expiration is longer than access token expiration
        if (jwtProperties.getRefreshTokenExpiration() <= jwtProperties.getAccessTokenExpiration()) {
            logger.warn("STARTUP_VALIDATION: Refresh token expiration ({} ms) should be longer than access token expiration ({} ms)", 
                       jwtProperties.getRefreshTokenExpiration(), jwtProperties.getAccessTokenExpiration());
        }
        
        if (isValid) {
            logger.info("STARTUP_VALIDATION: JWT configuration validation passed");
        } else {
            logger.warn("STARTUP_VALIDATION: JWT configuration validation completed with warnings - defaults will be used");
        }
        
        return true; // Always return true as we use defaults for invalid configurations
    }
    
    /**
     * Validates database connectivity and migrations.
     * 
     * @return true if database is accessible and migrations are applied
     */
    private boolean validateDatabaseConnectivity() {
        logger.debug("STARTUP_VALIDATION: Validating database connectivity");
        
        try (Connection connection = dataSource.getConnection()) {
            // Test basic connectivity
            if (connection.isValid(5)) {
                logger.debug("STARTUP_VALIDATION: Database connection is valid");
                
                // Check if migrations have been applied by verifying users table exists
                try {
                    var statement = connection.prepareStatement(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'users'"
                    );
                    var resultSet = statement.executeQuery();
                    
                    if (resultSet.next() && resultSet.getInt(1) > 0) {
                        logger.debug("STARTUP_VALIDATION: Database schema validation passed - users table exists");
                        
                        // Check if Flyway schema_version table exists (indicates migrations were run)
                        var flywayStatement = connection.prepareStatement(
                            "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'flyway_schema_history'"
                        );
                        var flywayResultSet = flywayStatement.executeQuery();
                        
                        if (flywayResultSet.next() && flywayResultSet.getInt(1) > 0) {
                            logger.info("STARTUP_VALIDATION: Database connectivity and migration validation passed");
                            return true;
                        } else {
                            logger.warn("STARTUP_VALIDATION: Flyway schema history table not found - migrations may not have been applied");
                            return false;
                        }
                    } else {
                        logger.error("STARTUP_VALIDATION: Users table not found - database schema not initialized");
                        return false;
                    }
                } catch (SQLException e) {
                    logger.error("STARTUP_VALIDATION: Database schema validation failed", e);
                    return false;
                }
            } else {
                logger.error("STARTUP_VALIDATION: Database connection is not valid");
                return false;
            }
        } catch (SQLException e) {
            logger.error("STARTUP_VALIDATION: Database connectivity validation failed", e);
            return false;
        }
    }
}