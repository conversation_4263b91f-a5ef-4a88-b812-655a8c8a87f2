package co.com.gedsys.authentication.controller.v1;

import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.ChangePasswordRequest;
import co.com.gedsys.authentication.dto.DetailedSessionInfo;
import co.com.gedsys.authentication.dto.FullNameRequest;
import co.com.gedsys.authentication.dto.FullNameResponse;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.dto.RefreshTokenRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.service.AuthenticationService;
import co.com.gedsys.authentication.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for authentication operations.
 * Handles login, logout, token refresh, profile management, and password changes.
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Autenticación", description = "Endpoints para autenticación, gestión de sesiones y perfil de usuario")
public class AuthenticationControllerV1 {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationControllerV1.class);
    private static final String KEY_ERROR = "error";
    private static final String KEY_MESSAGE = "message";

    private final AuthenticationService authenticationService;
    private final UserService userService;

    public AuthenticationControllerV1(AuthenticationService authenticationService, UserService userService) {
        this.authenticationService = authenticationService;
        this.userService = userService;
    }

    /**
     * Login endpoint with session type handling.
     * Supports both mobile and web sessions with optional push token registration.
     *
     * @param loginRequest the login request containing credentials and session info
     * @return authentication response with JWT and refresh tokens
     */
    @Operation(
        summary = "Iniciar sesión",
        description = "Autentica al usuario y crea una nueva sesión. Soporta sesiones móviles y web con registro opcional de token push."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Autenticación exitosa",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AuthResponse.class),
                examples = @ExampleObject(
                    value = """
                    {
                      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
                      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
                      "tokenType": "Bearer",
                      "expiresIn": 3600,
                      "user": {
                        "id": 1,
                        "email": "<EMAIL>",
                        "firstName": "John",
                        "lastName": "Doe",
                        "role": "USER",
                        "enabled": true,
                        "createdAt": "2024-01-01T12:00:00Z"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(responseCode = "400", description = "Datos de entrada inválidos"),
        @ApiResponse(responseCode = "401", description = "Credenciales incorrectas"),
        @ApiResponse(responseCode = "403", description = "Usuario deshabilitado")
    })
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        logger.info("SECURITY_EVENT: Login request - received login request for identifier: {} with session type: {}",
                   loginRequest.getIdentifier(), loginRequest.getSessionType());

        AuthResponse response = authenticationService.login(loginRequest);

        logger.info("SECURITY_EVENT: Login successful - user authenticated for identifier: {}", loginRequest.getIdentifier());
        return ResponseEntity.ok(response);
    }

    /**
     * Refresh token endpoint with single-use enforcement.
     * Validates and consumes the refresh token, generating new tokens.
     *
     * @param refreshTokenRequest the refresh token request
     * @return new authentication response with fresh tokens
     */
    @Operation(
        summary = "Renovar tokens",
        description = "Valida y consume el refresh token para generar nuevos tokens JWT. Los refresh tokens son de un solo uso."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Tokens renovados exitosamente",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AuthResponse.class)
            )
        ),
        @ApiResponse(responseCode = "400", description = "Refresh token inválido o expirado"),
        @ApiResponse(responseCode = "401", description = "Refresh token no válido")
    })
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest) {
        logger.debug("SECURITY_EVENT: Token refresh request - received token refresh request");

        AuthResponse response = authenticationService.refreshToken(refreshTokenRequest.getRefreshToken());

        logger.debug("SECURITY_EVENT: Token refresh successful - new tokens generated");
        return ResponseEntity.ok(response);
    }

    /**
     * Logout endpoint with proper cleanup.
     * Invalidates refresh tokens and removes push tokens based on session type.
     *
     * @param request the logout request containing session type
     * @return success response
     */
    @Operation(
        summary = "Cerrar sesión",
        description = "Invalida los tokens de refresh y elimina tokens push según el tipo de sesión. Requiere autenticación."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Sesión cerrada exitosamente",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = """
                    {
                      "message": "Logout successful"
                    }
                    """
                )
            )
        ),
        @ApiResponse(responseCode = "400", description = "Tipo de sesión inválido"),
        @ApiResponse(responseCode = "401", description = "No autenticado")
    })
    @SecurityRequirement(name = "Bearer Authentication")
    @PostMapping("/logout")
    public ResponseEntity<Map<String, String>> logout(@RequestBody Map<String, String> request) {
        logger.debug("SECURITY_EVENT: Logout request - received logout request");

        try {
            User currentUser = getCurrentUser();
            String sessionTypeStr = request.get("sessionType");

            if (sessionTypeStr != null) {
                SessionType sessionType = SessionType.valueOf(sessionTypeStr.toUpperCase());
                authenticationService.logout(currentUser, sessionType);
                logger.info("SECURITY_EVENT: Logout successful - user: {} logged out from session type: {}",
                           currentUser.getId(), sessionType);
            } else {
                // Logout from all sessions if no session type specified
                authenticationService.logoutFromAllSessions(currentUser);
                logger.info("SECURITY_EVENT: Global logout successful - user: {} logged out from all sessions",
                           currentUser.getId());
            }

            return ResponseEntity.ok(Map.of(KEY_MESSAGE, "Logout successful"));

        } catch (IllegalArgumentException e) {
            logger.warn("SECURITY_EVENT: Logout failed - invalid session type in request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(KEY_ERROR, "Invalid session type"));
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Logout error - unexpected error during logout: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(KEY_ERROR, "Logout failed"));
        }
    }

    /**
     * Profile endpoint for user information.
     * Returns the current authenticated user's profile information.
     *
     * @return user profile response
     */
    @Operation(
        summary = "Obtener perfil de usuario",
        description = "Retorna la información del perfil del usuario autenticado actualmente."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Perfil obtenido exitosamente",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = UserProfileResponse.class)
            )
        ),
        @ApiResponse(responseCode = "401", description = "No autenticado")
    })
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/profile")
    public ResponseEntity<UserProfileResponse> getProfile() {
        logger.debug("Profile request received");

        User currentUser = getCurrentUser();
        UserProfileResponse profile = userService.getUserProfile(currentUser.getId());

        logger.debug("Profile retrieved for user: {}", currentUser.getId());
        return ResponseEntity.ok(profile);
    }

    /**
     * Change password endpoint with validation.
     * Validates current password and updates to new password.
     * Invalidates all user sessions after successful password change.
     *
     * @param changePasswordRequest the password change request
     * @return success response
     */
    @Operation(
        summary = "Cambiar contraseña",
        description = "Cambia la contraseña del usuario autenticado. Invalida todas las sesiones del usuario después del cambio exitoso."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Contraseña cambiada exitosamente",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = """
                    {
                      "message": "Password changed successfully"
                    }
                    """
                )
            )
        ),
        @ApiResponse(responseCode = "400", description = "Datos de entrada inválidos o contraseña actual incorrecta"),
        @ApiResponse(responseCode = "401", description = "No autenticado")
    })
    @SecurityRequirement(name = "Bearer Authentication")
    @PutMapping("/change-password")
    public ResponseEntity<Map<String, String>> changePassword(
            @Valid @RequestBody ChangePasswordRequest changePasswordRequest) {
        logger.debug("SECURITY_EVENT: Password change request - received password change request");

        // Validate password confirmation
        if (!changePasswordRequest.isPasswordConfirmationValid()) {
            logger.warn("SECURITY_EVENT: Password change failed - password confirmation mismatch");
            return ResponseEntity.badRequest()
                    .body(Map.of(KEY_ERROR, "New password and confirmation do not match"));
        }

        // Validate new password is different
        if (!changePasswordRequest.isNewPasswordDifferent()) {
            logger.warn("SECURITY_EVENT: Password change failed - new password same as current password");
            return ResponseEntity.badRequest()
                    .body(Map.of(KEY_ERROR, "New password must be different from current password"));
        }

        User currentUser = getCurrentUser();

        // Change password
        userService.changePassword(
                currentUser.getId(),
                changePasswordRequest.getCurrentPassword(),
                changePasswordRequest.getNewPassword()
        );

        // Invalidate all sessions after password change for security
        authenticationService.logoutFromAllSessions(currentUser);

        logger.info("SECURITY_EVENT: Password change successful - password changed for user: {} and all sessions invalidated",
                   currentUser.getId());
        return ResponseEntity.ok(Map.of(KEY_MESSAGE, "Password changed successfully. Please login again."));
    }

    /**
     * Validate token endpoint.
     * Validates the current JWT token and returns user information.
     *
     * @return user profile if token is valid
     */
    @GetMapping("/validate")
    public ResponseEntity<UserProfileResponse> validateToken() {
        logger.debug("Token validation request received");

        User currentUser = getCurrentUser();

        UserProfileResponse profile = userService.getUserProfile(currentUser.getId());

        logger.debug("Token validation successful for user: {}", currentUser.getId());
        return ResponseEntity.ok(profile);
    }

    /**
     * Get current user's active sessions information.
     * Supports optional detailed information for admin users.
     *
     * @param detailed optional parameter to include detailed session information (admin only)
     * @return session information
     */
    @Operation(
        summary = "Obtener información de sesiones activas",
        description = "Obtiene información básica de las sesiones activas del usuario. Los administradores pueden solicitar información detallada usando el parámetro 'detailed=true'."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Información de sesiones obtenida exitosamente",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Basic session info",
                        value = """
                        {
                          "hasMobileSession": true,
                          "hasWebSession": false,
                          "totalActiveSessions": 1
                        }
                        """
                    ),
                    @ExampleObject(
                        name = "Detailed session info (admin only)",
                        value = """
                        {
                          "hasMobileSession": true,
                          "hasWebSession": false,
                          "totalActiveSessions": 1,
                          "detailedSessions": [
                            {
                              "username": "john.doe",
                              "sessionType": "MOBILE",
                              "clientIpAddress": "*************",
                              "userAgent": "Mozilla/5.0...",
                              "sessionCreatedAt": "2024-01-15T10:30:00",
                              "sessionExpiresAt": "2024-01-22T10:30:00",
                              "lastActivityAt": "2024-01-15T14:20:00",
                              "sessionId": "550e8400-e29b-41d4-a716-446655440000",
                              "expired": false,
                              "timeToExpirationSeconds": 604800
                            }
                          ]
                        }
                        """
                    )
                }
            )
        ),
        @ApiResponse(responseCode = "401", description = "No autenticado"),
        @ApiResponse(responseCode = "403", description = "Acceso denegado para información detallada (se requiere rol de administrador)")
    })
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/sessions")
    public ResponseEntity<Map<String, Object>> getActiveSessions(
            @RequestParam(value = "detailed", required = false, defaultValue = "false") boolean detailed) {
        logger.debug("Active sessions request received - detailed: {}", detailed);

        User currentUser = getCurrentUser();

        boolean hasMobileSession = authenticationService.hasActiveSession(currentUser, SessionType.MOBILE);
        boolean hasWebSession = authenticationService.hasActiveSession(currentUser, SessionType.WEB);
        long totalSessions = authenticationService.getActiveSessionCount(currentUser);

        Map<String, Object> sessionInfo = new java.util.HashMap<>();
        sessionInfo.put("hasMobileSession", hasMobileSession);
        sessionInfo.put("hasWebSession", hasWebSession);
        sessionInfo.put("totalActiveSessions", totalSessions);

        // Add detailed information if requested and user has admin role
        if (detailed) {
            try {
                // Check if user has admin role for detailed information
                boolean isAdmin = currentUser.getRole().name().equals("ADMIN");
                if (isAdmin) {
                    List<DetailedSessionInfo> detailedSessions = authenticationService.getDetailedActiveSessions(currentUser);
                    sessionInfo.put("detailedSessions", detailedSessions);

                    logger.info("SECURITY_EVENT: Detailed session info included - admin user: {} accessed detailed session data",
                               currentUser.getId());
                } else {
                    logger.warn("SECURITY_EVENT: Unauthorized detailed session request - non-admin user: {} attempted to access detailed session data",
                               currentUser.getId());
                    sessionInfo.put(KEY_ERROR, "Detailed session information requires administrator privileges");
                }
            } catch (Exception e) {
                logger.error("SECURITY_EVENT: Error retrieving detailed session info for user: {} - {}",
                            currentUser.getId(), e.getMessage());
                sessionInfo.put(KEY_ERROR, "Failed to retrieve detailed session information");
            }
        }

        logger.debug("Active sessions retrieved for user: {} - detailed: {}", currentUser.getId(), detailed);
        return ResponseEntity.ok(sessionInfo);
    }

    /**
     * Get full names of users by their usernames.
     * Allows authenticated users to resolve usernames to full names for display purposes.
     *
     * @param request the request containing list of usernames
     * @return map of username to full name
     */
    @Operation(
        summary = "Obtener nombres completos por usernames",
        description = "Obtiene los nombres completos de usuarios a partir de sus usernames. Los usuarios no encontrados no se incluyen en la respuesta."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Nombres completos obtenidos exitosamente",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FullNameResponse.class),
                examples = @ExampleObject(
                    value = """
                    {
                      "fullNames": {
                        "jdoe": "John Doe",
                        "msmith": "Mary Smith",
                        "agarcia": "Ana García"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(responseCode = "400", description = "Request inválido (lista vacía o demasiados usernames)"),
        @ApiResponse(responseCode = "401", description = "No autenticado")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/users/fullnames")
    public ResponseEntity<FullNameResponse> getFullNamesByUsernames(@Valid @RequestBody FullNameRequest request) {
        logger.debug("Full names request received for {} usernames", request.getUsernames().size());

        FullNameResponse response = userService.getFullNamesByUsernames(request.getUsernames());

        logger.debug("Full names retrieved: {} names found", response.getFullNames().size());
        return ResponseEntity.ok(response);
    }

    /**
     * Get the current authenticated user from security context.
     *
     * @return the current user
     * @throws ResponseStatusException if user is not authenticated or not found
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "User not authenticated");
        }

        String email = authentication.getName();
        return userService.findByEmail(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found: " + email));
    }



}