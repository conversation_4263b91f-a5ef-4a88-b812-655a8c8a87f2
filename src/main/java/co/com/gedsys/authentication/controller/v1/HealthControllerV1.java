package co.com.gedsys.authentication.controller.v1;

import co.com.gedsys.authentication.service.HealthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Custom health check controller for monitoring application components.
 * Provides detailed health information for database, JWT service, and application metrics.
 */
@RestController
@RequestMapping("/api/v1/health")
public class HealthControllerV1 {

    private static final Logger logger = LoggerFactory.getLogger(HealthControllerV1.class);
    private static final String STATUS = "status";

    private final HealthService healthService;

    public HealthControllerV1(HealthService healthService) {
        this.healthService = healthService;
    }

    /**
     * Basic health check endpoint.
     * Available to all users for basic service status.
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        try {
            Map<String, Object> health = healthService.getBasicHealth();

            String status = (String) health.get(STATUS);
            if ("DOWN".equals(status)) {
                return ResponseEntity.status(503).body(health);
            }

            return ResponseEntity.ok(health);

        } catch (Exception e) {
            logger.error("Health check endpoint failed", e);
            return ResponseEntity.status(500).body(Map.of(STATUS, "DOWN", "error", "Health check service unavailable"));
        }
    }

    /**
     * Detailed health check endpoint with metrics.
     * Only available to administrators.
     */
    @GetMapping("/detailed")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getDetailedHealth() {
        try {
            Map<String, Object> health = healthService.getDetailedHealth();

            String status = (String) health.get(STATUS);
            if ("DOWN".equals(status)) {
                return ResponseEntity.status(503).body(health);
            }

            return ResponseEntity.ok(health);

        } catch (Exception e) {
            logger.error("Detailed health check endpoint failed", e);
            return ResponseEntity.status(500).body(Map.of(STATUS, "DOWN", "error", "Health check service unavailable"));
        }
    }
}