package co.com.gedsys.authentication.controller.v1;

import co.com.gedsys.authentication.dto.DetailedSessionInfo;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.service.AuthenticationService;
import co.com.gedsys.authentication.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Min;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for administrative session management.
 * Provides endpoints for administrators to monitor and manage user sessions.
 * All endpoints require ADMIN role.
 */
@RestController
@RequestMapping("/api/v1/admin/sessions")
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "Administración de Sesiones", description = "Endpoints para gestión administrativa de sesiones activas (requiere rol ADMIN)")
@SecurityRequirement(name = "Bearer Authentication")
public class AdminSessionControllerV1 {

    private static final Logger logger = LoggerFactory.getLogger(AdminSessionControllerV1.class);

    private static final String KEY_ERROR = "error";
    private static final String KEY_MESSAGE = "message";
    private static final String KEY_SESSION_ID = "sessionId";

    private final AuthenticationService authenticationService;
    private final UserService userService;

    public AdminSessionControllerV1(AuthenticationService authenticationService, UserService userService) {
        this.authenticationService = authenticationService;
        this.userService = userService;
    }

    /**
     * Get detailed information about active sessions in the system.
     * Optionally filter by specific user ID.
     *
     * @param userId optional user ID to filter sessions
     * @return detailed session information
     */
    @Operation(
        summary = "Obtener información detallada de sesiones activas",
        description = "Obtiene información detallada de sesiones activas del sistema. Sin parámetro userId muestra todas las sesiones. Con userId muestra solo las sesiones de ese usuario. Solo disponible para administradores."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Información de sesiones obtenida exitosamente",
            content = @Content(
                mediaType = "application/json",
                array = @ArraySchema(schema = @Schema(implementation = DetailedSessionInfo.class))
            )
        ),
        @ApiResponse(responseCode = "400", description = "Parámetros inválidos"),
        @ApiResponse(responseCode = "401", description = "No autenticado"),
        @ApiResponse(responseCode = "403", description = "Acceso denegado - se requiere rol de administrador"),
        @ApiResponse(responseCode = "404", description = "Usuario no encontrado")
    })
    @GetMapping
    public ResponseEntity<List<DetailedSessionInfo>> getActiveSessions(
            @Parameter(description = "ID del usuario para filtrar sesiones (opcional)")
            @RequestParam(value = "userId", required = false)
            @Min(value = 1, message = "User ID must be positive")
            Long userId) {

        User currentAdmin = getCurrentUser();
        logger.info("SECURITY_EVENT: Admin session query request from admin: {} ({}), userId filter: {}",
                   currentAdmin.getId(), currentAdmin.getEmail(), userId);

        List<DetailedSessionInfo> sessions;

        if (userId != null) {
            // Get sessions for specific user
            User targetUser = userService.findById(userId);
            sessions = authenticationService.getDetailedActiveSessions(targetUser);

            logger.info("SECURITY_EVENT: Admin detailed session query - admin: {} retrieved {} sessions for user: {} ({})",
                       currentAdmin.getId(), sessions.size(), userId, targetUser.getEmail());
        } else {
            // Get all system sessions with detailed logging
            logger.debug("SECURITY_EVENT: Initiating global session query for admin: {}", currentAdmin.getId());
            sessions = authenticationService.getAllDetailedActiveSessions();

            // Log summary by session type for debugging
            long mobileCount = sessions.stream().filter(s -> s.getSessionType() == SessionType.MOBILE).count();
            long webCount = sessions.stream().filter(s -> s.getSessionType() == SessionType.WEB).count();

            logger.info("SECURITY_EVENT: Admin global session query - admin: {} retrieved {} total sessions (Mobile: {}, Web: {})",
                       currentAdmin.getId(), sessions.size(), mobileCount, webCount);

            // Log unique users with sessions for debugging
            long uniqueUsers = sessions.stream().map(DetailedSessionInfo::getUsername).distinct().count();
            logger.debug("SECURITY_EVENT: Sessions belong to {} unique users", uniqueUsers);
        }

        return ResponseEntity.ok(sessions);
    }

    /**
     * Invalidate a specific session by sessionId.
     * This endpoint allows administrators to terminate individual user sessions
     * without affecting other sessions of the same user.
     *
     * @param sessionId the session ID to invalidate
     * @return response indicating success or failure
     */
    @Operation(
        summary = "Invalidar sesión específica por ID de sesión",
        description = "Invalida una sesión específica utilizando su ID de sesión. Esta acción terminará inmediatamente la sesión especificada sin afectar otras sesiones del mismo usuario. Solo disponible para administradores."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Sesión invalidada exitosamente"
        ),
        @ApiResponse(responseCode = "400", description = "ID de sesión inválido o sesión no encontrada"),
        @ApiResponse(responseCode = "401", description = "No autenticado"),
        @ApiResponse(responseCode = "403", description = "Acceso denegado - se requiere rol de administrador"),
        @ApiResponse(responseCode = "500", description = "Error interno del servidor")
    })
    @DeleteMapping("/{sessionId}")
    public ResponseEntity<Map<String, String>> invalidateSession(
            @Parameter(description = "ID de la sesión a invalidar")
            @PathVariable String sessionId) {

        User currentAdmin = getCurrentUser();
        String safeSessionId = abbreviateId(sessionId);
        if (logger.isInfoEnabled()) {
            logger.info(
                "SECURITY_EVENT: Admin session invalidation request - admin: {} ({}) requesting invalidation of sessionId: {}",
                currentAdmin.getId(), currentAdmin.getEmail(), safeSessionId
            );
        }

        try {
            // Validate sessionId parameter
            if (sessionId == null || sessionId.trim().isEmpty()) {
                logger.warn("SECURITY_EVENT: Invalid session invalidation request - sessionId is null or empty from admin: {}",
                           currentAdmin.getId());
                return ResponseEntity.badRequest()
                        .body(Map.of(KEY_ERROR, "Session ID cannot be null or empty"));
            }

            // Invalidate the session
            authenticationService.invalidateSessionBySessionId(sessionId.trim());

            if (logger.isInfoEnabled()) {
                logger.info(
                    "SECURITY_EVENT: Admin session invalidation successful - admin: {} successfully invalidated sessionId: {}",
                    currentAdmin.getId(), safeSessionId
                );
            }

            return ResponseEntity.ok(Map.of(
                KEY_MESSAGE, "Session invalidated successfully",
                KEY_SESSION_ID, safeSessionId
            ));

        } catch (IllegalArgumentException e) {
            logger.warn("SECURITY_EVENT: Admin session invalidation failed - admin: {} - {}",
                       currentAdmin.getId(), e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of(KEY_ERROR, e.getMessage()));

        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Admin session invalidation error - admin: {} - unexpected error: {}",
                        currentAdmin.getId(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(KEY_ERROR, "Failed to invalidate session"));
        }
    }

    private static String abbreviateId(String id) {
        if (id == null || id.isBlank()) {
            return "null";
        }
        return id.length() <= 8 ? id : id.substring(0, 8) + "...";
    }

    /**
     * Get the current authenticated user.
     * 
     * @return current user
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        return userService.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("Admin user not found: " + email));
    }
}