package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.PasswordRecoveryCode;
import co.com.gedsys.authentication.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PasswordRecoveryCodeRepository extends JpaRepository<PasswordRecoveryCode, Long> {

    Optional<PasswordRecoveryCode> findByCodeAndUsedFalse(String code);

    List<PasswordRecoveryCode> findByUserAndUsedFalse(User user);

    @Modifying
    @Query("UPDATE PasswordRecoveryCode p SET p.used = true, p.usedAt = :usedAt WHERE p.user = :user AND p.used = false")
    void invalidateAllActiveCodesByUser(@Param("user") User user, @Param("usedAt") LocalDateTime usedAt);

    @Modifying
    @Query("DELETE FROM PasswordRecoveryCode p WHERE p.expiresAt < :expirationTime")
    void deleteExpiredCodes(@Param("expirationTime") LocalDateTime expirationTime);

    @Query("SELECT COUNT(p) FROM PasswordRecoveryCode p WHERE p.user = :user AND p.createdAt > :since")
    long countByUserAndCreatedAtAfter(@Param("user") User user, @Param("since") LocalDateTime since);

    boolean existsByCode(String code);
}