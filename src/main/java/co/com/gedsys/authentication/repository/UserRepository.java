package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for User entity operations.
 * Provides basic CRUD operations and custom queries for user management.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * Find a user by email address.
     * 
     * @param email the email address to search for
     * @return Optional containing the user if found, empty otherwise
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Check if a user exists with the given email address.
     * 
     * @param email the email address to check
     * @return true if a user exists with the email, false otherwise
     */
    boolean existsByEmail(String email);
    
    /**
     * Find users with optional filters and pagination (legacy method for backward compatibility).
     * This query supports filtering by email (partial match), enabled status, and role.
     *
     * @param email partial email to filter by (case-insensitive), null to ignore
     * @param enabled enabled status to filter by, null to ignore
     * @param role role to filter by, null to ignore
     * @param pageable pagination and sorting parameters
     * @return Page of users matching the criteria
     */
    @Query("SELECT u FROM User u WHERE " +
       "(:email IS NULL OR u.email LIKE %:email%) AND " +
       "((:status IS NULL AND u.status != 'DELETED') OR u.status = :status) AND " +
       "(:enabled IS NULL OR u.enabled = :enabled) AND " +
       "(:role IS NULL OR u.role = :role)")
Page<User> findUsersWithFilters(
        @Param("email") String email,
        @Param("status") UserStatus status,
        @Param("enabled") Boolean enabled,
        @Param("role") Role role,
        Pageable pageable
);


    
    /**
     * Find all enabled users.
     * 
     * @param pageable pagination and sorting parameters
     * @return Page of enabled users
     */
    Page<User> findByEnabledTrue(Pageable pageable);
    
    /**
     * Find users by role.
     * 
     * @param role the role to filter by
     * @param pageable pagination and sorting parameters
     * @return Page of users with the specified role
     */
    Page<User> findByRole(Role role, Pageable pageable);
    
    /**
     * Count users by enabled status.
     * 
     * @param enabled the enabled status to count
     * @return number of users with the specified enabled status
     */
    long countByEnabled(boolean enabled);
    
    /**
     * Count users by role.
     *
     * @param role the role to count
     * @return number of users with the specified role
     */
    long countByRole(Role role);

    /**
     * Find a user by username.
     *
     * @param username the username to search for
     * @return Optional containing the user if found, empty otherwise
     */
    Optional<User> findByUsername(String username);

    /**
     * Find a user by username or email for flexible login.
     * This method searches first by username, then by email.
     *
     * @param identifier the username or email to search for
     * @return Optional containing the user if found, empty otherwise
     */
    @Query("SELECT u FROM User u WHERE u.username = :identifier OR u.email = :identifier")
    Optional<User> findByUsernameOrEmail(@Param("identifier") String identifier);

    /**
     * Check if a username exists (only active and inactive users).
     *
     * @param username the username to check
     * @return true if a user exists with the username, false otherwise
     */
    boolean existsByUsername(String username);

    /**
     * Check if a username exists, including deleted users.
     * Used for username generation to avoid conflicts with deleted users.
     *
     * @param username the username to check
     * @return true if a user exists with the username (including deleted), false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE u.username = :username")
    boolean existsByUsernameIncludingDeleted(@Param("username") String username);

    /**
     * Check if an email exists, including deleted users.
     * Used for registration validation to prevent duplicate emails.
     *
     * @param email the email to check
     * @return true if a user exists with the email (including deleted), false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE LOWER(u.email) = LOWER(:email)")
    boolean existsByEmailIncludingDeleted(@Param("email") String email);

    /**
     * Find users by status with pagination.
     *
     * @param status the user status to filter by
     * @param pageable pagination and sorting parameters
     * @return Page of users with the specified status
     */
    Page<User> findByStatus(UserStatus status, Pageable pageable);

    /**
     * Find users by status and enabled flag.
     *
     * @param status the user status to filter by
     * @param enabled the enabled flag to filter by
     * @return List of users matching the criteria
     */
    List<User> findByStatusAndEnabled(UserStatus status, boolean enabled);

    /**
     * Find users by username list, excluding deleted users.
     * Used for bulk lookup operations like full name resolution.
     *
     * @param usernames list of usernames to search for
     * @return List of active users matching the usernames
     */
    @Query("SELECT u FROM User u WHERE u.username IN :usernames AND u.status != 'DELETED'")
    List<User> findByUsernameIn(@Param("usernames") List<String> usernames);
}