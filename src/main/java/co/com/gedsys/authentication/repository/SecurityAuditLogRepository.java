package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.SecurityAuditLog;
import co.com.gedsys.authentication.entity.SecurityEventType;
import co.com.gedsys.authentication.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SecurityAuditLogRepository extends JpaRepository<SecurityAuditLog, Long> {

    Page<SecurityAuditLog> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);

    Page<SecurityAuditLog> findByEventTypeOrderByCreatedAtDesc(SecurityEventType eventType, Pageable pageable);

    List<SecurityAuditLog> findByUserAndEventTypeAndCreatedAtAfter(User user, SecurityEventType eventType, 
                                                                    LocalDateTime since);

    @Query("SELECT s FROM SecurityAuditLog s WHERE s.ipAddress = :ipAddress AND s.eventType = :eventType " +
           "AND s.createdAt > :since ORDER BY s.createdAt DESC")
    List<SecurityAuditLog> findByIpAddressAndEventTypeAndCreatedAtAfter(@Param("ipAddress") String ipAddress,
                                                                        @Param("eventType") SecurityEventType eventType,
                                                                        @Param("since") LocalDateTime since);

    @Query("SELECT COUNT(s) FROM SecurityAuditLog s WHERE s.user = :user AND s.eventType = :eventType " +
           "AND s.createdAt > :since")
    long countByUserAndEventTypeAndCreatedAtAfter(@Param("user") User user, 
                                                  @Param("eventType") SecurityEventType eventType,
                                                  @Param("since") LocalDateTime since);
}