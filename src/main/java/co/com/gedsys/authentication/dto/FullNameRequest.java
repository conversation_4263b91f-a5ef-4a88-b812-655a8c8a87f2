package co.com.gedsys.authentication.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Request para obtener nombres completos de usuarios a partir de sus usernames")
public class FullNameRequest {

    @NotEmpty(message = "La lista de usernames no puede estar vacía")
    @Size(max = 100, message = "Máximo 100 usernames por request")
    @Schema(description = "Lista de usernames para obtener sus nombres completos", 
            example = "[\"jdoe\", \"msmith\", \"agarcia\"]")
    private List<String> usernames;

    public FullNameRequest() {}

    public FullNameRequest(List<String> usernames) {
        this.usernames = usernames;
    }

    public List<String> getUsernames() {
        return usernames;
    }

    public void setUsernames(List<String> usernames) {
        this.usernames = usernames;
    }
}