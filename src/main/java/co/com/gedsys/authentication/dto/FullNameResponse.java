package co.com.gedsys.authentication.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

@Schema(description = "Response con nombres completos de usuarios mapeados por username")
public class FullNameResponse {

    @Schema(description = "Mapeo de username a nombre completo (firstName + lastName)", 
            example = "{\"jdoe\": \"John Doe\", \"msmith\": \"Mary <PERSON>\", \"agarcia\": \"Ana García\"}")
    private Map<String, String> fullNames;

    public FullNameResponse() {}

    public FullNameResponse(Map<String, String> fullNames) {
        this.fullNames = fullNames;
    }

    public Map<String, String> getFullNames() {
        return fullNames;
    }

    public void setFullNames(Map<String, String> fullNames) {
        this.fullNames = fullNames;
    }
}