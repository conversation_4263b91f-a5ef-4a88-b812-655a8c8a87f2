package co.com.gedsys.authentication.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

public class PasswordRecoveryCodeResponse {

    private boolean success;
    private PasswordRecoveryData data;

    public PasswordRecoveryCodeResponse() {
        this.success = true;
    }

    public PasswordRecoveryCodeResponse(String code, String email, 
                                      LocalDateTime expiresAt, LocalDateTime createdAt) {
        this.success = true;
        this.data = new PasswordRecoveryData(code, email, expiresAt, createdAt);
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public PasswordRecoveryData getData() {
        return data;
    }

    public void setData(PasswordRecoveryData data) {
        this.data = data;
    }

    public static class PasswordRecoveryData {
        private String code;
        
        private String email;
        
        @JsonProperty("expiresAt")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime expiresAt;
        
        @JsonProperty("createdAt")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;

        public PasswordRecoveryData() {}

        public PasswordRecoveryData(String code, String email, 
                                  LocalDateTime expiresAt, LocalDateTime createdAt) {
            this.code = code;
            this.email = email;
            this.expiresAt = expiresAt;
            this.createdAt = createdAt;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public LocalDateTime getExpiresAt() {
            return expiresAt;
        }

        public void setExpiresAt(LocalDateTime expiresAt) {
            this.expiresAt = expiresAt;
        }

        public LocalDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
        }
    }
}