package co.com.gedsys.authentication.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

public class PasswordRecoveryErrorResponse {

    private boolean success = false;
    private ErrorDetails error;

    public PasswordRecoveryErrorResponse() {}

    public PasswordRecoveryErrorResponse(String code, String message) {
        this.error = new ErrorDetails(code, message);
    }

    public PasswordRecoveryErrorResponse(String code, String message, LocalDateTime timestamp) {
        this.error = new ErrorDetails(code, message, timestamp);
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public ErrorDetails getError() {
        return error;
    }

    public void setError(ErrorDetails error) {
        this.error = error;
    }

    public static class ErrorDetails {
        private String code;
        private String message;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime timestamp;

        public ErrorDetails() {
            this.timestamp = LocalDateTime.now();
        }

        public ErrorDetails(String code, String message) {
            this();
            this.code = code;
            this.message = message;
        }

        public ErrorDetails(String code, String message, LocalDateTime timestamp) {
            this.code = code;
            this.message = message;
            this.timestamp = timestamp;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public LocalDateTime getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(LocalDateTime timestamp) {
            this.timestamp = timestamp;
        }
    }
}