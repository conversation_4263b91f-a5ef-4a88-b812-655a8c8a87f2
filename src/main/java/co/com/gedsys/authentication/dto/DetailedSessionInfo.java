package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.SessionType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * DTO for detailed session information including comprehensive session metadata.
 * Used for administrative session monitoring and user session management.
 */
@Schema(description = "Información detallada de sesión activa")
public class DetailedSessionInfo {
    
    @Schema(description = "Nombre de usuario activo", example = "john.doe")
    private String username;
    
    @Schema(description = "Tipo de sesión", example = "MOBILE")
    private SessionType sessionType;
    
    @Schema(description = "Dirección IP del cliente", example = "*************")
    private String clientIpAddress;
    
    @Schema(description = "Agente de usuario del cliente", example = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)")
    private String userAgent;
    
    @Schema(description = "Fecha y hora de creación de la sesión")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime sessionCreatedAt;
    
    @Schema(description = "Fecha y hora de expiración de la sesión")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime sessionExpiresAt;
    
    @Schema(description = "Fecha y hora de última actividad")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastActivityAt;
    
    @Schema(description = "ID único de la sesión para seguimiento", example = "550e8400-e29b-41d4-a716-446655440000")
    private String sessionId;
    
    @Schema(description = "Indica si la sesión está expirada")
    private boolean expired;
    
    @Schema(description = "Tiempo restante hasta la expiración en segundos")
    private Long timeToExpirationSeconds;
    
    // Default constructor
    public DetailedSessionInfo() {}
    
    // Constructor with all required fields
    public DetailedSessionInfo(String username, SessionType sessionType, String clientIpAddress, 
                             String userAgent, LocalDateTime sessionCreatedAt, LocalDateTime sessionExpiresAt, 
                             LocalDateTime lastActivityAt, String sessionId) {
        this.username = username;
        this.sessionType = sessionType;
        this.clientIpAddress = clientIpAddress;
        this.userAgent = userAgent;
        this.sessionCreatedAt = sessionCreatedAt;
        this.sessionExpiresAt = sessionExpiresAt;
        this.lastActivityAt = lastActivityAt;
        this.sessionId = sessionId;
        this.expired = sessionExpiresAt != null && LocalDateTime.now().isAfter(sessionExpiresAt);
        this.timeToExpirationSeconds = calculateTimeToExpiration();
    }
    
    // Getters and Setters
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public SessionType getSessionType() {
        return sessionType;
    }
    
    public void setSessionType(SessionType sessionType) {
        this.sessionType = sessionType;
    }
    
    public String getClientIpAddress() {
        return clientIpAddress;
    }
    
    public void setClientIpAddress(String clientIpAddress) {
        this.clientIpAddress = clientIpAddress;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public LocalDateTime getSessionCreatedAt() {
        return sessionCreatedAt;
    }
    
    public void setSessionCreatedAt(LocalDateTime sessionCreatedAt) {
        this.sessionCreatedAt = sessionCreatedAt;
    }
    
    public LocalDateTime getSessionExpiresAt() {
        return sessionExpiresAt;
    }
    
    public void setSessionExpiresAt(LocalDateTime sessionExpiresAt) {
        this.sessionExpiresAt = sessionExpiresAt;
        this.expired = sessionExpiresAt != null && LocalDateTime.now().isAfter(sessionExpiresAt);
        this.timeToExpirationSeconds = calculateTimeToExpiration();
    }
    
    public LocalDateTime getLastActivityAt() {
        return lastActivityAt;
    }
    
    public void setLastActivityAt(LocalDateTime lastActivityAt) {
        this.lastActivityAt = lastActivityAt;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public boolean isExpired() {
        return expired;
    }
    
    public void setExpired(boolean expired) {
        this.expired = expired;
    }
    
    public Long getTimeToExpirationSeconds() {
        return timeToExpirationSeconds;
    }
    
    public void setTimeToExpirationSeconds(Long timeToExpirationSeconds) {
        this.timeToExpirationSeconds = timeToExpirationSeconds;
    }
    
    // Utility methods
    private Long calculateTimeToExpiration() {
        if (sessionExpiresAt == null) {
            return null;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(sessionExpiresAt)) {
            return 0L; // Already expired
        }
        
        return java.time.Duration.between(now, sessionExpiresAt).getSeconds();
    }
    
    /**
     * Check if the session is active (not expired and not used).
     * 
     * @return true if session is active
     */
    public boolean isActive() {
        return !expired && sessionExpiresAt != null && LocalDateTime.now().isBefore(sessionExpiresAt);
    }
    
    /**
     * Get a masked version of the session ID for logging purposes.
     * 
     * @return masked session ID
     */
    public String getMaskedSessionId() {
        if (sessionId == null || sessionId.length() < 8) {
            return sessionId;
        }
        return sessionId.substring(0, 8) + "...";
    }
    
    /**
     * Get a truncated user agent for display purposes.
     * 
     * @return truncated user agent
     */
    public String getTruncatedUserAgent() {
        if (userAgent == null) {
            return null;
        }
        return userAgent.length() > 100 ? userAgent.substring(0, 100) + "..." : userAgent;
    }
    
    @Override
    public String toString() {
        return "DetailedSessionInfo{" +
                "username='" + username + '\'' +
                ", sessionType=" + sessionType +
                ", clientIpAddress='" + clientIpAddress + '\'' +
                ", userAgent='" + getTruncatedUserAgent() + '\'' +
                ", sessionCreatedAt=" + sessionCreatedAt +
                ", sessionExpiresAt=" + sessionExpiresAt +
                ", lastActivityAt=" + lastActivityAt +
                ", sessionId='" + getMaskedSessionId() + '\'' +
                ", expired=" + expired +
                ", timeToExpirationSeconds=" + timeToExpirationSeconds +
                '}';
    }
}
