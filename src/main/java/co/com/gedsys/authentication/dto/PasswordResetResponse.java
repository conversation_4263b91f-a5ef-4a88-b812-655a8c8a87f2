package co.com.gedsys.authentication.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

public class PasswordResetResponse {

    private boolean success;
    private String message;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;

    public PasswordResetResponse() {
        this.success = true;
        this.timestamp = LocalDateTime.now();
    }

    public PasswordResetResponse(String message) {
        this();
        this.message = message;
    }

    public PasswordResetResponse(boolean success, String message) {
        this(message);
        this.success = success;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}