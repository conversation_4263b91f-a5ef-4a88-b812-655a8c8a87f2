package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.SessionType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * DTO for login requests with session type and optional push token information.
 */
public class LoginRequest {
    
    @NotBlank(message = "Username or email is required")
    private String identifier;
    
    @NotBlank(message = "Password is required")
    @Size(min = 6, message = "Password must be at least 6 characters")
    private String password;
    
    @NotNull(message = "Session type is required")
    private SessionType sessionType;
    
    // Optional fields for mobile sessions
    private String pushToken;
    private DeviceType deviceType;
    private String deviceId;
    
    // Default constructor
    public LoginRequest() {}
    
    // Constructor with required fields
    public LoginRequest(String identifier, String password, SessionType sessionType) {
        this.identifier = identifier;
        this.password = password;
        this.sessionType = sessionType;
    }

    // Constructor with all fields
    public LoginRequest(String identifier, String password, SessionType sessionType,
                       String pushToken, DeviceType deviceType, String deviceId) {
        this.identifier = identifier;
        this.password = password;
        this.sessionType = sessionType;
        this.pushToken = pushToken;
        this.deviceType = deviceType;
        this.deviceId = deviceId;
    }
    
    // Getters and Setters
    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    // Backward compatibility methods
    @Deprecated
    public String getEmail() {
        return identifier;
    }

    @Deprecated
    public void setEmail(String email) {
        this.identifier = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public SessionType getSessionType() {
        return sessionType;
    }
    
    public void setSessionType(SessionType sessionType) {
        this.sessionType = sessionType;
    }
    
    public String getPushToken() {
        return pushToken;
    }
    
    public void setPushToken(String pushToken) {
        this.pushToken = pushToken;
    }
    
    public DeviceType getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(DeviceType deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    // Utility methods
    public boolean isMobileSession() {
        return SessionType.MOBILE.equals(sessionType);
    }
    
    public boolean hasPushTokenInfo() {
        return pushToken != null && !pushToken.trim().isEmpty();
    }
}