package co.com.gedsys.authentication.exception;

public class RateLimitExceededException extends RuntimeException {

    public RateLimitExceededException(String message) {
        super(message);
    }

    public RateLimitExceededException() {
        super("Se ha excedido el límite de intentos de recuperación");
    }
    
    public static RateLimitExceededException forIdentifier(String identifier) {
        return new RateLimitExceededException("Se ha excedido el límite de intentos de recuperación para " + identifier);
    }
}