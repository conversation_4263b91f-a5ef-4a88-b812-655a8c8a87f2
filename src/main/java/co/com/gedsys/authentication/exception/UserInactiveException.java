package co.com.gedsys.authentication.exception;

public class UserInactiveException extends RuntimeException {

    public UserInactiveException(String message) {
        super(message);
    }

    public UserInactiveException() {
        super("El usuario no está activo");
    }
    
    public static UserInactiveException forIdentifier(String identifier) {
        return new UserInactiveException("El usuario " + identifier + " no está activo y no puede recuperar la contraseña");
    }
}