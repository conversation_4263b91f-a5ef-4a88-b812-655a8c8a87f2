package co.com.gedsys.authentication.exception;

public class InvalidRecoveryCodeException extends RuntimeException {

    public InvalidRecoveryCodeException(String message) {
        super(message);
    }

    public InvalidRecoveryCodeException(String code, String message) {
        super("Código de recuperación inválido " + code + ": " + message);
    }

    public InvalidRecoveryCodeException() {
        super("El código de recuperación proporcionado no es válido");
    }
}