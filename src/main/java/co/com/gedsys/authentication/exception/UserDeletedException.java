package co.com.gedsys.authentication.exception;

/**
 * Exception thrown when attempting to perform operations on a deleted user.
 * For security reasons, this is typically handled as a UserNotFoundException.
 */
public class UserDeletedException extends RuntimeException {
    
    public UserDeletedException(String message) {
        super(message);
    }
    
    public UserDeletedException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public UserDeletedException(Long userId) {
        super("User with ID " + userId + " has been deleted");
    }
    
    public UserDeletedException(String identifier, String identifierType) {
        super("User with " + identifierType + " '" + identifier + "' has been deleted");
    }
}
