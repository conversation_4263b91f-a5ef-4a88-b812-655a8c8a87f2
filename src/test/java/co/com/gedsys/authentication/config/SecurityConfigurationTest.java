package co.com.gedsys.authentication.config;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for SecurityConfiguration.
 * Verifies that security configuration works correctly without AuthenticationProvider
 * and that UserDetailsService is properly configured.
 */
@SpringBootTest
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@Tag("integration")
@DisplayName("SecurityConfiguration Integration Tests")
class SecurityConfigurationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private SecurityFilterChain securityFilterChain;

    @Test
    @DisplayName("Should verify UserDetailsService is configured without AuthenticationProvider warnings")
    void securityConfiguration_UserDetailsService_ConfiguredCorrectly() {
        // Given & When
        // The application context should load without AuthenticationProvider warnings
        
        // Then
        assertThat(userDetailsService).isNotNull();
        assertThat(userDetailsService.getClass().getSimpleName()).isEqualTo("UserDetailsServiceImpl");
    }

    @Test
    @DisplayName("Should verify AuthenticationProvider bean is not present")
    void securityConfiguration_AuthenticationProvider_NotPresent() {
        // Given & When
        boolean authProviderExists = false;
        try {
            applicationContext.getBean(AuthenticationProvider.class);
            authProviderExists = true;
        } catch (Exception ignored) {
            // Expected - AuthenticationProvider should not exist
        }

        // Then
        assertThat(authProviderExists).isFalse();
    }

    @Test
    @DisplayName("Should verify SecurityFilterChain bean is present")
    void securityConfiguration_SecurityFilterChain_Present() {
        // Given & When & Then
        assertThat(securityFilterChain).isNotNull();
        assertThat(securityFilterChain.getClass().getSimpleName()).contains("FilterChain");
    }

    @Test
    @DisplayName("Should verify application context loads successfully")
    void securityConfiguration_ApplicationContext_LoadsSuccessfully() {
        // Given & When & Then
        assertThat(applicationContext).isNotNull();
        assertThat(applicationContext.containsBean("securityFilterChain")).isTrue();
        assertThat(applicationContext.containsBean("corsConfigurationSource")).isTrue();
    }

    @Test
    @DisplayName("Should verify CORS configuration bean is present")
    void securityConfiguration_CorsConfiguration_Present() {
        // Given & When & Then
        assertThat(applicationContext.containsBean("corsConfigurationSource")).isTrue();
    }

    @Test
    @DisplayName("Should verify JWT authentication filter is configured")
    void securityConfiguration_JwtFilter_Configured() {
        // Given & When & Then
        assertThat(applicationContext.containsBean("jwtAuthenticationFilter")).isTrue();
    }

    @Test
    @DisplayName("Should verify custom authentication entry point is configured")
    void securityConfiguration_CustomAuthenticationEntryPoint_Configured() {
        // Given & When & Then
        assertThat(applicationContext.containsBean("customAuthenticationEntryPoint")).isTrue();
    }
}