package co.com.gedsys.authentication.config;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {JwtProperties.class})
@EnableConfigurationProperties(JwtProperties.class)
@ActiveProfiles("test")
@Tag("integration")
@TestPropertySource(properties = {
    "app.jwt.secret=test-secret-key-for-testing-purposes-must-be-long-enough",
    "app.jwt.access-token-expiration=900000",
    "app.jwt.refresh-token-expiration=604800000",
    "app.jwt.mobile.access-token-expiration=1800000",
    "app.jwt.web.access-token-expiration=900000"
})
class ConfigurationValidationTest {

    @Autowired
    private JwtProperties jwtProperties;

    @Test
    void shouldLoadJwtPropertiesWithOptimizedDefaults() {
        assertThat(jwtProperties.getSecret()).isEqualTo("test-secret-key-for-testing-purposes-must-be-long-enough");
        assertThat(jwtProperties.getAccessTokenExpiration()).isEqualTo(900000L); // 15 minutes
        assertThat(jwtProperties.getRefreshTokenExpiration()).isEqualTo(604800000L); // 7 days
        assertThat(jwtProperties.getMobile().getAccessTokenExpiration()).isEqualTo(1800000L); // 30 minutes
        assertThat(jwtProperties.getWeb().getAccessTokenExpiration()).isEqualTo(900000L); // 15 minutes
    }

    @Test
    void shouldHaveSecureTokenExpirationTimes() {
        // Validate that token expiration times are within secure ranges
        long fifteenMinutes = 15 * 60 * 1000; // 900000ms
        long thirtyMinutes = 30 * 60 * 1000; // 1800000ms
        long sevenDays = 7 * 24 * 60 * 60 * 1000; // 604800000ms

        assertThat(jwtProperties.getAccessTokenExpiration())
            .as("Default access token should be 15 minutes")
            .isEqualTo(fifteenMinutes);

        assertThat(jwtProperties.getWeb().getAccessTokenExpiration())
            .as("Web access token should be 15 minutes")
            .isEqualTo(fifteenMinutes);

        assertThat(jwtProperties.getMobile().getAccessTokenExpiration())
            .as("Mobile access token should be 30 minutes")
            .isEqualTo(thirtyMinutes);

        assertThat(jwtProperties.getRefreshTokenExpiration())
            .as("Refresh token should be 7 days")
            .isEqualTo(sevenDays);
    }

    @Test
    void shouldHaveMinimumSecretLength() {
        assertThat(jwtProperties.getSecret().length())
            .as("JWT secret should be at least 32 characters for security")
            .isGreaterThanOrEqualTo(32);
    }
}