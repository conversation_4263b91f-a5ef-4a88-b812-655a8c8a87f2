package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.service.UserDetailsServiceImpl.CustomUserDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for UserDetailsServiceImpl.
 * Tests all authentication scenarios and security validations.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("UserDetailsServiceImpl Tests")
class UserDetailsServiceImplTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserDetailsServiceImpl userDetailsService;

    private User validUser;
    private User deletedUser;
    private User disabledUser;
    private User inactiveUser;

    @BeforeEach
    void setUp() {
        validUser = createUser(1L, "testuser", "<EMAIL>", "password", 
                              UserStatus.ACTIVE, true, Role.USER);
        
        deletedUser = createUser(2L, "deleteduser", "<EMAIL>", "password", 
                                UserStatus.DELETED, true, Role.USER);
        
        disabledUser = createUser(3L, "disableduser", "<EMAIL>", "password", 
                                 UserStatus.ACTIVE, false, Role.USER);
        
        inactiveUser = createUser(4L, "inactiveuser", "<EMAIL>", "password", 
                                 UserStatus.INACTIVE, true, Role.USER);
    }

    @Test
    @DisplayName("Should load user details successfully for valid active user")
    void loadUserByUsername_ValidUser_ReturnsUserDetails() {
        // Given
        when(userRepository.findByUsernameOrEmail("testuser")).thenReturn(Optional.of(validUser));

        // When
        UserDetails userDetails = userDetailsService.loadUserByUsername("testuser");

        // Then
        assertThat(userDetails).isNotNull();
        assertThat(userDetails.getUsername()).isEqualTo("testuser");
        assertThat(userDetails.getPassword()).isEqualTo("password");
        assertThat(userDetails.isEnabled()).isTrue();
        assertThat(userDetails.isAccountNonExpired()).isTrue();
        assertThat(userDetails.isAccountNonLocked()).isTrue();
        assertThat(userDetails.isCredentialsNonExpired()).isTrue();
        
        // Verify authorities
        assertThat(userDetails.getAuthorities()).hasSize(1);
        GrantedAuthority authority = userDetails.getAuthorities().iterator().next();
        assertThat(authority.getAuthority()).isEqualTo("ROLE_USER");
    }

    @Test
    @DisplayName("Should load user details successfully for admin user")
    void loadUserByUsername_AdminUser_ReturnsUserDetailsWithAdminRole() {
        // Given
        User adminUser = createUser(5L, "admin", "<EMAIL>", "password", 
                                   UserStatus.ACTIVE, true, Role.ADMIN);
        when(userRepository.findByUsernameOrEmail("admin")).thenReturn(Optional.of(adminUser));

        // When
        UserDetails userDetails = userDetailsService.loadUserByUsername("admin");

        // Then
        assertThat(userDetails.getAuthorities()).hasSize(1);
        GrantedAuthority authority = userDetails.getAuthorities().iterator().next();
        assertThat(authority.getAuthority()).isEqualTo("ROLE_ADMIN");
    }

    @Test
    @DisplayName("Should throw UsernameNotFoundException when user not found")
    void loadUserByUsername_NonExistentUser_ThrowsException() {
        // Given
        when(userRepository.findByUsernameOrEmail(anyString())).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("nonexistent"))
                .isInstanceOf(UsernameNotFoundException.class)
                .hasMessage("Invalid credentials");
    }

    @Test
    @DisplayName("Should throw UsernameNotFoundException for deleted user")
    void loadUserByUsername_DeletedUser_ThrowsException() {
        // Given
        when(userRepository.findByUsernameOrEmail("deleteduser")).thenReturn(Optional.of(deletedUser));

        // When & Then
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("deleteduser"))
                .isInstanceOf(UsernameNotFoundException.class)
                .hasMessage("Invalid credentials");
    }

    @Test
    @DisplayName("Should throw UsernameNotFoundException for disabled user")
    void loadUserByUsername_DisabledUser_ThrowsException() {
        // Given
        when(userRepository.findByUsernameOrEmail("disableduser")).thenReturn(Optional.of(disabledUser));

        // When & Then
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("disableduser"))
                .isInstanceOf(UsernameNotFoundException.class)
                .hasMessage("Invalid credentials");
    }

    @Test
    @DisplayName("Should throw UsernameNotFoundException for inactive user")
    void loadUserByUsername_InactiveUser_ThrowsException() {
        // Given
        when(userRepository.findByUsernameOrEmail("inactiveuser")).thenReturn(Optional.of(inactiveUser));

        // When & Then
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("inactiveuser"))
                .isInstanceOf(UsernameNotFoundException.class)
                .hasMessage("Invalid credentials");
    }

    @Test
    @DisplayName("Should load user by email address")
    void loadUserByUsername_EmailAddress_ReturnsUserDetails() {
        // Given
        when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.of(validUser));

        // When
        UserDetails userDetails = userDetailsService.loadUserByUsername("<EMAIL>");

        // Then
        assertThat(userDetails).isNotNull();
        assertThat(userDetails.getUsername()).isEqualTo("testuser");
    }

    @Test
    @DisplayName("CustomUserDetails should correctly map user properties")
    void customUserDetails_CorrectlyMapsUserProperties() {
        // Given
        CustomUserDetails customUserDetails = new CustomUserDetails(validUser);

        // When & Then
        assertThat(customUserDetails.getUser()).isEqualTo(validUser);
        assertThat(customUserDetails.getUsername()).isEqualTo(validUser.getUsername());
        assertThat(customUserDetails.getPassword()).isEqualTo(validUser.getPassword());
        assertThat(customUserDetails.isEnabled()).isTrue();
        assertThat(customUserDetails.isAccountNonExpired()).isTrue();
        assertThat(customUserDetails.isAccountNonLocked()).isTrue();
        assertThat(customUserDetails.isCredentialsNonExpired()).isTrue();
    }

    @Test
    @DisplayName("CustomUserDetails should be disabled for inactive user")
    void customUserDetails_InactiveUser_IsDisabled() {
        // Given
        CustomUserDetails customUserDetails = new CustomUserDetails(inactiveUser);

        // When & Then
        assertThat(customUserDetails.isEnabled()).isFalse();
    }

    @Test
    @DisplayName("CustomUserDetails should be disabled for disabled user")
    void customUserDetails_DisabledUser_IsDisabled() {
        // Given
        CustomUserDetails customUserDetails = new CustomUserDetails(disabledUser);

        // When & Then
        assertThat(customUserDetails.isEnabled()).isFalse();
    }

    @Test
    @DisplayName("CustomUserDetails toString should not expose sensitive information")
    void customUserDetails_ToString_DoesNotExposeSensitiveInfo() {
        // Given
        CustomUserDetails customUserDetails = new CustomUserDetails(validUser);

        // When
        String toString = customUserDetails.toString();

        // Then
        assertThat(toString).contains("userId=1");
        assertThat(toString).contains("username='testuser'");
        assertThat(toString).contains("email='<EMAIL>'");
        assertThat(toString).contains("role=USER");
        assertThat(toString).contains("status=ACTIVE");
        assertThat(toString).contains("enabled=true");
        assertThat(toString).doesNotContain("password"); // Should not expose password
    }

    /**
     * Helper method to create a User entity for testing.
     */
    private User createUser(Long id, String username, String email, String password, 
                           UserStatus status, boolean enabled, Role role) {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(password);
        user.setFirstName("Test");
        user.setLastName("User");
        user.setStatus(status);
        user.setEnabled(enabled);
        user.setRole(role);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        return user;
    }
}