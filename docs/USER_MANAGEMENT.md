# User Management Enhancements

## Overview

The GEDSYS Authentication Service has been enhanced with advanced user management features including automatic username generation, flexible authentication, and logical deletion capabilities. These enhancements improve user experience while maintaining backward compatibility.

## Key Features

### 1. Automatic Username Generation

#### Algorithm
The system automatically generates unique usernames for all users based on their first and last names:

1. **Extract Components**: Take first initial of firstName + full lastName
2. **Normalize**: Convert to lowercase
3. **Sanitize**: Remove diacritics (accents), spaces, and special characters
4. **Uniqueness Check**: Verify against all users (including deleted ones)
5. **Increment**: Add numbers for duplicates (e.g., "jdoe", "jdoe2", "jdoe3")

#### Examples
```
<PERSON> → "jdoe"
<PERSON> → "jsmith"
<PERSON> → "j<PERSON><PERSON>"
<PERSON> → "mlopez"
<PERSON> (duplicate) → "jdoe2"
```

#### Implementation
The `UsernameGenerator` component handles all username generation logic:

```java
@Component
public class UsernameGenerator {
    public String generateUsername(String firstName, String lastName, UserRepository userRepository) {
        // Implementation details in the service class
    }
}
```

### 2. Flexible Authentication

#### Login Options
Users can now authenticate using either:
- **Username**: Auto-generated unique identifier (e.g., "jdoe")
- **Email**: Traditional email-based authentication

#### API Changes
The login endpoint now accepts an `identifier` field:

```json
{
  "identifier": "jdoe",  // Can be username or email
  "password": "password123",
  "sessionType": "MOBILE"
}
```

#### Backward Compatibility
- The deprecated `email` field is still supported
- Existing API clients continue to work without changes
- Migration is seamless for existing users

### 3. User Status Management

#### Status Types
Enhanced user lifecycle management with three status levels:

- **ACTIVE**: User can access the system (default for new users)
- **INACTIVE**: User is disabled but not deleted (equivalent to enabled=false)
- **DELETED**: User is logically deleted (data preserved for audit purposes)

#### Logical Deletion
- User data is preserved for audit and compliance purposes
- Deleted users cannot authenticate or be found in normal queries
- Username and email remain reserved to prevent conflicts
- All active sessions are invalidated upon deletion

#### Migration Strategy
Existing users are migrated based on their `enabled` status:
- `enabled = true` → `status = 'ACTIVE'`
- `enabled = false` → `status = 'INACTIVE'`

## Database Schema Changes

### New Columns
The `users` table has been enhanced with two new columns:

```sql
ALTER TABLE users ADD COLUMN username VARCHAR(100);
ALTER TABLE users ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' 
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED'));
```

### New Indexes
Performance optimization indexes have been added:

```sql
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
```

### Constraints
- Username uniqueness is enforced at the application level
- Status values are constrained to valid enum values
- Email uniqueness includes deleted users

## Repository Enhancements

### New Query Methods
The `UserRepository` has been extended with new methods:

```java
// Username-based queries
Optional<User> findByUsername(String username);
Optional<User> findByUsernameOrEmail(String identifier);

// Uniqueness checks including deleted users
boolean existsByUsernameIncludingDeleted(String username);
boolean existsByEmailIncludingDeleted(String email);

// Status-based queries
Page<User> findByStatus(UserStatus status, Pageable pageable);
```

### Enhanced Filtering
Admin user listing now supports status filtering:

```java
Page<User> findUsersWithFilters(
    String email,
    Boolean enabled,
    Role role,
    UserStatus status,  // New parameter
    Pageable pageable
);
```

## Service Layer Changes

### UserService Enhancements
- **Automatic Username Generation**: Integrated into user creation process
- **Enhanced Validation**: Checks against deleted users for uniqueness
- **Soft Delete**: New `softDeleteUser()` method for logical deletion
- **Improved Error Messages**: More specific error handling

### AuthenticationService Updates
- **Flexible Login**: Supports both username and email authentication
- **Sequential Search**: First tries username, then email
- **Status Validation**: Prevents deleted users from authenticating

## API Changes

### Login Endpoint
**Before:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**After:**
```json
{
  "identifier": "jdoe",  // Username or email
  "password": "password123"
}
```

### User Profile Response
**Before:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```

**After:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "jdoe",  // New field
  "firstName": "John",
  "lastName": "Doe"
}
```

### Admin User Creation
Username is automatically generated and returned:

```json
{
  "id": 2,
  "email": "<EMAIL>",
  "username": "jsmith",  // Auto-generated
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "USER",
  "enabled": true
}
```

## Testing Coverage

### Username Generation Tests
- ✅ Basic username generation from names
- ✅ Duplicate handling with incremental numbers
- ✅ Special character and diacritic removal
- ✅ Edge cases (null/empty names)
- ✅ Uniqueness validation including deleted users

### Flexible Authentication Tests
- ✅ Login with username
- ✅ Login with email
- ✅ Invalid identifier handling
- ✅ Backward compatibility with email field

### User Status Tests
- ✅ Status migration from enabled field
- ✅ Logical deletion functionality
- ✅ Status-based query filtering
- ✅ Deleted user authentication prevention

## Migration Guide

### For Existing Users
1. **Automatic Migration**: V7 migration automatically adds username and status fields
2. **Username Generation**: Application generates usernames on first startup
3. **Status Setting**: Existing users get status based on enabled field
4. **No Downtime**: Migration is backward compatible

### For API Clients
1. **Immediate**: Start using `identifier` field instead of `email` for login
2. **Optional**: Update to handle `username` field in responses
3. **Gradual**: Deprecated `email` field will be supported for transition period

### For Administrators
1. **User Creation**: Usernames are now automatically generated
2. **User Management**: New status-based filtering options available
3. **Monitoring**: Enhanced logging for username generation and flexible login

## Security Considerations

### Username Uniqueness
- Usernames are checked against all users including deleted ones
- Prevents confusion and potential security issues
- Maintains audit trail integrity

### Authentication Security
- Flexible login doesn't compromise security
- Same password validation and rate limiting apply
- Deleted users cannot authenticate regardless of method

### Data Integrity
- Logical deletion preserves audit trails
- Foreign key relationships remain intact
- Historical data remains accessible for compliance

## Performance Impact

### Database Performance
- New indexes on username and status fields
- Optimized queries for flexible authentication
- Minimal impact on existing query performance

### Application Performance
- Username generation is performed once per user
- Caching can be implemented for frequently accessed usernames
- Flexible login adds minimal overhead

## Future Enhancements

### Planned Features
- [ ] Username customization by users
- [ ] Bulk username regeneration utility
- [ ] Advanced user status workflows
- [ ] Username history tracking

### Considerations
- Username change functionality
- Advanced filtering and search capabilities
- Integration with external identity providers
- Multi-tenant username scoping

## Troubleshooting

### Common Issues

#### Username Generation Failures
- **Cause**: Missing first or last name
- **Solution**: Ensure both names are provided during user creation

#### Duplicate Username Conflicts
- **Cause**: High volume of users with similar names
- **Solution**: System automatically handles with incremental numbers

#### Migration Issues
- **Cause**: Existing data inconsistencies
- **Solution**: Run data validation before migration

### Monitoring
- Monitor username generation performance
- Track flexible login usage patterns
- Alert on unusual authentication failures

## Support

For issues related to user management enhancements:
1. Check application logs for username generation errors
2. Verify database migration completion
3. Test flexible login with both username and email
4. Contact development team for complex issues