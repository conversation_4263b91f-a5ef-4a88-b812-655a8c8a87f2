{"openapi": "3.0.1", "info": {"title": "Default module", "description": "", "version": "1.0.0"}, "tags": [{"name": "Administración"}, {"name": "Administración de Sesiones"}, {"name": "Autenticación"}, {"name": "Health"}, {"name": "<PERSON><PERSON>"}, {"name": "Password Recovery"}, {"name": "Admin"}], "paths": {"/identity/api/v1/admin/users": {"get": {"summary": "Listar usuarios con paginación", "deprecated": false, "description": "Obtiene una lista paginada de usuarios con filtros opcionales. Requiere rol de administrador.", "operationId": "getUsers", "tags": ["Administración"], "parameters": [{"name": "email", "in": "query", "description": "Filtro por email (coincidencia parcial)", "required": false, "schema": {"type": "string", "example": "<EMAIL>"}}, {"name": "enabled", "in": "query", "description": "Filtro por estado habilitado", "required": false, "schema": {"type": "boolean"}}, {"name": "status", "in": "query", "description": "Filtro por estado del usuario", "required": false, "schema": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "DELETED"]}}, {"name": "role", "in": "query", "description": "Filtro por rol del usuario", "required": false, "schema": {"type": "string", "enum": ["USER", "ADMIN"]}}, {"name": "page", "in": "query", "description": "Número de página (base 0)", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}, {"name": "size", "in": "query", "description": "Tamaño de página", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "sort", "in": "query", "description": "Criterios de ordenación (formato: campo,dirección)", "required": false, "schema": {"type": "string", "default": "createdAt,desc", "example": "email,asc"}}], "responses": {"200": {"description": "Lista de usuarios obtenida exitosamente", "content": {"application/json": {"schema": {"type": "object", "properties": {"content": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "email": {"type": "string"}, "username": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "role": {"type": "string"}, "status": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}}, "required": ["id", "email", "username", "firstName", "lastName", "role", "status", "enabled", "createdAt"]}}, "page": {"type": "integer"}, "size": {"type": "integer"}, "totalElements": {"type": "integer"}, "totalPages": {"type": "integer"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}}, "required": ["content", "page", "size", "totalElements", "totalPages", "first", "last"]}}}, "headers": {}}, "400": {"description": "Parámetros de paginación inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}, "post": {"summary": "Crear nuevo usuario", "deprecated": false, "description": "Crea un nuevo usuario en el sistema. Requiere rol de administrador.", "operationId": "createUser", "tags": ["Administración"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}, "example": ""}}}, "responses": {"201": {"description": "Usuario creado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "409": {"description": "El usuario ya existe", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/users/{userId}": {"get": {"summary": "Obtener detalles de usuario por ID", "deprecated": false, "description": "Obtiene información detallada de un usuario específico incluyendo sesiones activas y tokens push.", "operationId": "getUserById", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Detalles del usuario obtenidos exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminUserResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}, "put": {"summary": "Actualizar usuario", "deprecated": false, "description": "Actualiza la información de un usuario existente. Si se cambia la contraseña, se invalidan todas las sesiones del usuario.", "operationId": "updateUser", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}, "example": ""}}}, "responses": {"200": {"description": "Usuario actualizado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}, "delete": {"summary": "Eliminar usuario", "deprecated": false, "description": "Elimina un usuario del sistema. Invalida todas las sesiones y remueve tokens asociados.", "operationId": "deleteUser", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Usuario eliminado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "User deleted successfully"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/users/{userId}/deactivate": {"put": {"summary": "Desactivar usuario", "deprecated": false, "description": "Cambia el estado del usuario a INACTIVE.", "operationId": "deactivateUser", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Usuario desactivado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "User deactivated successfully"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/users/{userId}/reactivate": {"put": {"summary": "Reactivar usuario", "deprecated": false, "description": "Cambia el estado del usuario a ACTIVE.", "operationId": "reactivateUser", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Usuario reactivado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "User reactivated successfully"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/users/{userId}/enabled": {"patch": {"summary": "Habilitar o deshabilitar usuario", "deprecated": false, "description": "Cambia el estado enabled del usuario. Si se deshabilita, se invalidan todas las sesiones.", "operationId": "setUserEnabled", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["enabled"], "properties": {"enabled": {"type": "boolean", "description": "Estado habilitado del usuario"}}}, "example": ""}}}, "responses": {"200": {"description": "Estado del usuario cambiado exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "enabled"], "properties": {"message": {"type": "string", "example": "User enabled successfully"}, "enabled": {"type": "boolean", "example": true}}}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/users/{userId}/invalidate-sessions": {"post": {"summary": "Invalidar todas las sesiones de un usuario", "deprecated": false, "description": "Invalida todas las sesiones activas de un usuario específico.", "operationId": "invalidateUserSessions", "tags": ["Administración"], "parameters": [{"name": "userId", "in": "path", "description": "ID del usuario", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Sesiones invalidadas exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "All user sessions invalidated successfully"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/users/stats": {"get": {"summary": "Obtener estadísticas de usuarios", "deprecated": false, "description": "Obtiene estadísticas generales sobre los usuarios del sistema.", "operationId": "getUserStats", "tags": ["Administración"], "parameters": [], "responses": {"200": {"description": "Estadísticas obtenidas exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["totalUsers", "enabledUsers", "disabledUsers", "adminUsers", "regularUsers", "totalPushTokens"], "properties": {"totalUsers": {"type": "integer", "format": "int64", "description": "Total de usuarios en el sistema"}, "enabledUsers": {"type": "integer", "format": "int64", "description": "Usuarios habilitados"}, "disabledUsers": {"type": "integer", "format": "int64", "description": "Usuarios deshabilitados"}, "adminUsers": {"type": "integer", "format": "int64", "description": "Usuarios con rol de administrador"}, "regularUsers": {"type": "integer", "format": "int64", "description": "Usuarios con rol regular"}, "totalPushTokens": {"type": "integer", "format": "int64", "description": "Total de tokens push registrados"}}}, "example": {"totalUsers": 150, "enabledUsers": 140, "disabledUsers": 10, "adminUsers": 5, "regularUsers": 145, "totalPushTokens": 85}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/sessions": {"get": {"summary": "Obtener información detallada de sesiones activas", "deprecated": false, "description": "Obtiene información detallada de sesiones activas del sistema. Sin parámetro userId muestra todas las sesiones. Con userId muestra solo las sesiones de ese usuario. Solo disponible para administradores.", "operationId": "getActiveSessions", "tags": ["Administración de Sesiones"], "parameters": [{"name": "userId", "in": "query", "description": "ID del usuario para filtrar sesiones (opcional)", "required": false, "example": 123, "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "Información de sesiones obtenida exitosamente", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedSessionInfo"}}, "examples": {"1": {"summary": "Todas las sesiones del sistema", "value": [{"username": "john.doe", "sessionType": "MOBILE", "clientIpAddress": "*************", "userAgent": "MyApp/1.0 (Android 12)", "sessionCreatedAt": "2024-01-15T10:30:00", "sessionExpiresAt": "2024-01-22T10:30:00", "lastActivityAt": "2024-01-15T14:20:00", "sessionId": "550e8400-e29b-41d4-a716-446655440000", "expired": false, "timeToExpirationSeconds": 604800}, {"username": "jane.smith", "sessionType": "WEB", "clientIpAddress": "*************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "sessionCreatedAt": "2024-01-15T09:00:00", "sessionExpiresAt": "2024-01-15T12:00:00", "lastActivityAt": "2024-01-15T11:45:00", "sessionId": "660f9511-f40c-52e5-b827-557766551111", "expired": false, "timeToExpirationSeconds": 900}]}, "2": {"summary": "Sesiones de un usuario específico", "value": [{"username": "john.doe", "sessionType": "MOBILE", "clientIpAddress": "*************", "userAgent": "MyApp/1.0 (Android 12)", "sessionCreatedAt": "2024-01-15T10:30:00", "sessionExpiresAt": "2024-01-22T10:30:00", "lastActivityAt": "2024-01-15T14:20:00", "sessionId": "550e8400-e29b-41d4-a716-446655440000", "expired": false, "timeToExpirationSeconds": 604800}]}}}}, "headers": {}}, "400": {"description": "Parámetros <PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/admin/sessions/{sessionId}": {"delete": {"summary": "Invalidar sesión específica por ID de sesión", "deprecated": false, "description": "Invalida una sesión específica utilizando su ID de sesión. Esta acción terminará inmediatamente la sesión especificada sin afectar otras sesiones del mismo usuario. Solo disponible para administradores.", "operationId": "invalidateSession", "tags": ["Administración de Sesiones"], "parameters": [{"name": "sessionId", "in": "path", "description": "ID de la sesión a invalidar", "required": true, "example": "550e8400-e29b-41d4-a716-446655440000", "schema": {"type": "string", "minLength": 1}}], "responses": {"200": {"description": "Sesión invalidada exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "sessionId"], "properties": {"message": {"type": "string", "example": "Session invalidated successfully"}, "sessionId": {"type": "string", "description": "ID de la sesión invalidada (puede estar abreviado para logs)", "example": "550e8400..."}}}}}, "headers": {}}, "400": {"description": "ID de sesión inválido o sesión no encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"1": {"summary": "ID de sesión vacío", "value": {"error": "Session ID cannot be null or empty"}}, "2": {"summary": "Sesión no encontrada", "value": {"error": "Session not found or already expired"}}}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "500": {"description": "Error interno del servidor", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Failed to invalidate session"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/login": {"post": {"summary": "In<PERSON><PERSON>", "deprecated": false, "description": "Autentica al usuario y crea una nueva sesión. Soporta sesiones móviles y web con registro opcional de token push.", "operationId": "login", "tags": ["Autenticación"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}, "example": {"identifier": "<EMAIL>", "password": "password123", "sessionType": "WEB"}}}}, "responses": {"200": {"description": "Autenticación exitosa", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}, "example": {"accessToken": "eyJhbGciOiJIUzI1NiIs...", "refreshToken": "eyJhbGciOiJIUzI1NiIs...", "tokenType": "Bearer", "expiresIn": 3600, "user": {"id": 1, "email": "<EMAIL>", "username": "john.doe", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "role": "USER", "createdAt": "2024-01-01T12:00:00Z"}}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "Credenciales incorrectas", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "<PERSON><PERSON><PERSON> deshabili<PERSON>o", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/refresh": {"post": {"summary": "Renovar tokens", "deprecated": false, "description": "Valida y consume el refresh token para generar nuevos tokens JWT. Los refresh tokens son de un solo uso.", "operationId": "refreshToken", "tags": ["Autenticación"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}, "example": ""}}}, "responses": {"200": {"description": "Tokens renovados exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}, "headers": {}}, "400": {"description": "Refresh token inválido o expirado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "Refresh token no válido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/logout": {"post": {"summary": "<PERSON><PERSON><PERSON>", "deprecated": false, "description": "Invalida los tokens de refresh y elimina tokens push según el tipo de sesión. Requiere autenticación.", "operationId": "logout", "tags": ["Autenticación"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión a cerrar. Si no se especifica, cierra todas las sesiones."}}}, "example": ""}}}, "responses": {"200": {"description": "Sesión cerrada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "Logout successful"}}}, "headers": {}}, "400": {"description": "Tipo de sesión inválido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/profile": {"get": {"summary": "Obtener perfil de usuario", "deprecated": false, "description": "Retorna la información del perfil del usuario autenticado actualmente.", "operationId": "getProfile", "tags": ["Autenticación"], "parameters": [], "responses": {"200": {"description": "Perfil obtenido exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/change-password": {"put": {"summary": "Cambiar contraseña", "deprecated": false, "description": "Cambia la contraseña del usuario autenticado. Invalida todas las sesiones del usuario después del cambio exitoso.", "operationId": "changePassword", "tags": ["Autenticación"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}, "example": ""}}}, "responses": {"200": {"description": "Contraseña cambiada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "example": {"message": "Password changed successfully. Please login again."}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos o contraseña actual incorrecta", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/validate": {"get": {"summary": "Validar token", "deprecated": false, "description": "Valida el token JWT actual y retorna información del usuario.", "operationId": "validateToken", "tags": ["Autenticación"], "parameters": [], "responses": {"200": {"description": "Token válido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "headers": {}}, "401": {"description": "Token inválido o expirado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/sessions": {"get": {"summary": "Obtener información de sesiones activas", "deprecated": false, "description": "Obtiene información básica de las sesiones activas del usuario. Los administradores pueden solicitar información detallada usando el parámetro 'detailed=true'.", "operationId": "getActiveSessions", "tags": ["Autenticación"], "parameters": [{"name": "detailed", "in": "query", "description": "Incluir información detallada de sesiones (solo para administradores)", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Información de sesiones obtenida exitosamente", "content": {"application/json": {"schema": {"type": "object", "properties": {"hasMobileSession": {"type": "boolean", "description": "Indica si el usuario tiene una sesión móvil activa"}, "hasWebSession": {"type": "boolean", "description": "Indica si el usuario tiene una sesión web activa"}, "totalActiveSessions": {"type": "integer", "description": "Número total de sesiones activas"}, "detailedSessions": {"type": "array", "items": {"$ref": "#/components/schemas/DetailedSessionInfo"}, "description": "Información detallada de sesiones (solo para administradores)"}, "error": {"type": "string", "description": "Error al obtener información detallada"}}}, "examples": {"1": {"summary": "Información básica de sesiones", "value": {"hasMobileSession": true, "hasWebSession": false, "totalActiveSessions": 1}}, "2": {"summary": "Información detallada (admin)", "value": {"hasMobileSession": true, "hasWebSession": false, "totalActiveSessions": 1, "detailedSessions": [{"username": "john.doe", "sessionType": "MOBILE", "clientIpAddress": "*************", "userAgent": "Mozilla/5.0...", "sessionCreatedAt": "2024-01-15T10:30:00", "sessionExpiresAt": "2024-01-22T10:30:00", "lastActivityAt": "2024-01-15T14:20:00", "sessionId": "550e8400-e29b-41d4-a716-446655440000", "expired": false, "timeToExpirationSeconds": 604800}]}}}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado para información detallada (se requiere rol de administrador)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/auth/users/fullnames": {"post": {"summary": "Obtener nombres completos por usernames", "deprecated": false, "description": "Obtiene los nombres completos de usuarios a partir de sus usernames. Los usuarios no encontrados no se incluyen en la respuesta.", "operationId": "getFullNamesByUsernames", "tags": ["Autenticación"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FullNameRequest"}, "example": {"usernames": ["jdoe", "msmith", "agarcia"]}}}}, "responses": {"200": {"description": "Nombres completos obtenidos exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FullNameResponse"}, "examples": {"1": {"summary": "<PERSON><PERSON><PERSON><PERSON> nombres encontrados", "value": {"fullNames": {"jdoe": "<PERSON>", "msmith": "<PERSON>", "agarcia": "<PERSON>"}}}, "2": {"summary": "Algunos usernames no encontrados", "value": {"fullNames": {"jdoe": "<PERSON>", "agarcia": "<PERSON>"}}}, "3": {"summary": "Ningún username encontrado", "value": {"fullNames": {}}}}}}, "headers": {}}, "400": {"description": "Request inválido (lista vacía o demasiados usernames)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/health/status": {"get": {"summary": "Verificación básica de salud", "deprecated": false, "description": "Endpoint básico de verificación de salud del sistema. Disponible para todos los usuarios para verificar el estado del servicio.", "operationId": "getHealthStatus", "tags": ["Health"], "parameters": [], "responses": {"200": {"description": "Sistema funcionando correctamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasicHealthResponse"}, "example": {"status": "UP", "timestamp": "2024-01-01T12:00:00Z", "uptime": "2h 30m 45s", "version": "1.0.0"}}}, "headers": {}}, "500": {"description": "Error interno del servicio de salud", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorHealthResponse"}, "example": {"status": "DOWN", "error": "Health check service unavailable"}}}, "headers": {}}, "503": {"description": "Sistema no disponible", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BasicHealthResponse"}, "example": {"status": "DOWN", "timestamp": "2024-01-01T12:00:00Z", "error": "Database connection failed"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/health/detailed": {"get": {"summary": "Verificación detallada de salud con métricas", "deprecated": false, "description": "Endpoint de verificación de salud detallada con información de componentes y métricas del sistema. Solo disponible para administradores.", "operationId": "getDetailedHealth", "tags": ["Health", "Admin"], "parameters": [], "responses": {"200": {"description": "Información detallada de salud obtenida exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailedHealthResponse"}, "example": {"status": "UP", "timestamp": "2024-01-01T12:00:00Z", "uptime": "2h 30m 45s", "version": "1.0.0", "components": {"database": {"status": "UP", "responseTime": "15ms", "activeConnections": 5, "maxConnections": 20}, "jwt": {"status": "UP", "tokensIssued": 150, "tokensValidated": 2500}, "diskSpace": {"status": "UP", "free": "15.2 GB", "total": "50 GB"}}, "metrics": {"totalUsers": 150, "activeSessions": 45, "totalRequests": 10000, "averageResponseTime": "120ms"}}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - se requiere rol de administrador", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "500": {"description": "Error interno del servicio de salud", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorHealthResponse"}, "example": {"status": "DOWN", "error": "Health check service unavailable"}}}, "headers": {}}, "503": {"description": "Sistema no disponible", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailedHealthResponse"}, "example": {"status": "DOWN", "timestamp": "2024-01-01T12:00:00Z", "components": {"database": {"status": "DOWN", "error": "Connection timeout"}, "jwt": {"status": "UP"}}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/v1/push-token": {"post": {"summary": "Registrar token push", "deprecated": false, "description": "Registra o actualiza un token de notificaciones push para el usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "registerPushToken", "tags": ["<PERSON><PERSON>"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTokenRequest"}, "example": ""}}}, "responses": {"200": {"description": "Token push registrado exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "pushToken"], "properties": {"message": {"type": "string", "example": "Push token registered successfully"}, "pushToken": {"$ref": "#/components/schemas/PushTokenInfo"}}}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}, "headers": {}}}, "security": [{"bearer": []}]}, "get": {"summary": "Obtener información del token push", "deprecated": false, "description": "Obtiene la información del token push del usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "getPushToken", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Información del token push obtenida exitosamente", "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "required": ["message", "pushToken"], "properties": {"message": {"type": "string", "example": "Push token retrieved successfully"}, "pushToken": {"$ref": "#/components/schemas/PushTokenInfo"}}}, {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "example": "No push token registered"}}}]}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}, "headers": {}}}, "security": [{"bearer": []}]}, "put": {"summary": "Actualizar token push", "deprecated": false, "description": "Actualiza un token push existente para el usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "updatePushToken", "tags": ["<PERSON><PERSON>"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushTokenRequest"}, "example": ""}}}, "responses": {"200": {"description": "Token push actualizado exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["message", "pushToken"], "properties": {"message": {"type": "string", "example": "Push token updated successfully"}, "pushToken": {"$ref": "#/components/schemas/PushTokenInfo"}}}}}, "headers": {}}, "400": {"description": "Datos de entrada inválidos o no existe token para actualizar", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"1": {"summary": "No existe token para actualizar", "value": {"error": "No existing push token to update. Use POST to register a new token."}}, "2": {"summary": "<PERSON><PERSON>", "value": {"error": "Invalid device type"}}}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}, "headers": {}}}, "security": [{"bearer": []}]}, "delete": {"summary": "Eliminar token push", "deprecated": false, "description": "Elimina el token push del usuario autenticado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "deletePushToken", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Token push eliminado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}, "examples": {"1": {"summary": "Token eliminado", "value": {"message": "Push token deleted successfully"}}, "2": {"summary": "Token no encontrado", "value": {"message": "No push token found to delete"}}}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/identity/api/v1/password-recovery/{identifier}/code": {"post": {"summary": "Generar código de recuperación de contraseña", "deprecated": false, "description": "Genera un código seguro de recuperación para el restablecimiento de contraseña. El identificador puede ser email o username.", "operationId": "generateRecoveryCode", "tags": ["Password Recovery"], "parameters": [{"name": "identifier", "in": "path", "description": "Identificador del usuario (email o username)", "required": true, "schema": {"type": "string"}}], "responses": {"201": {"description": "Código de recuperación generado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordRecoveryCodeResponse"}, "example": {"success": true, "data": {"code": "A1B2C3D4", "email": "<EMAIL>", "expiresAt": "2025-09-24T15:00:00", "createdAt": "2025-09-24T14:00:00"}}}}, "headers": {}}, "403": {"description": "Usuario inactivo", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordRecoveryErrorResponse"}}}, "headers": {}}, "404": {"description": "Usuario no encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordRecoveryErrorResponse"}}}, "headers": {}}, "429": {"description": "Límite de intentos excedido", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordRecoveryErrorResponse"}}}, "headers": {}}}}}, "/identity/api/v1/password-recovery/{identifier}": {"patch": {"summary": "Restablecer contraseña con código de recuperación", "deprecated": false, "description": "Valida el código de recuperación y actualiza la contraseña del usuario", "operationId": "resetPassword", "tags": ["Password Recovery"], "parameters": [{"name": "identifier", "in": "path", "description": "Identificador del usuario (email o username)", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetRequest"}, "example": {"code": "A1B2C3D4", "newPassword": "NuevaContraseña123!"}}}}, "responses": {"200": {"description": "Contraseña restablecida exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetResponse"}, "example": {"success": true, "message": "Contraseña actualizada exitosamente", "timestamp": "2025-09-24T14:00:00"}}}, "headers": {}}, "400": {"description": "<PERSON><PERSON><PERSON>, expirado o ya usado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordRecoveryErrorResponse"}, "examples": {"invalidCode": {"summary": "<PERSON><PERSON><PERSON>", "value": {"success": false, "error": {"code": "INVALID_CODE", "message": "El código proporcionado no es válido", "timestamp": "2025-09-24T14:00:00"}}}, "expiredCode": {"summary": "<PERSON><PERSON><PERSON> expirado", "value": {"success": false, "error": {"code": "EXPIRED_CODE", "message": "El código de recuperación ha expirado", "timestamp": "2025-09-24T14:00:00"}}}}}}, "headers": {}}}}}, "/api/v1/push-token/exists": {"get": {"summary": "Verificar existencia de token push", "deprecated": false, "description": "Verifica si el usuario autenticado tiene un token push registrado. Solo disponible para usuarios con sesiones móviles activas.", "operationId": "hasPushToken", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Verificación completada exitosamente", "content": {"application/json": {"schema": {"type": "object", "required": ["hasPushToken", "message"], "properties": {"hasPushToken": {"type": "boolean", "description": "Indica si el usuario tiene un token push registrado", "example": true}, "message": {"type": "string", "description": "Mensaje descriptivo del resultado", "example": "User has push token"}}}}}, "headers": {}}, "401": {"description": "No autenticado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "403": {"description": "Acceso denegado - solo disponible para sesiones móviles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Push tokens can only be managed from mobile sessions"}}}, "headers": {}}}, "security": [{"bearer": []}]}}}, "components": {"schemas": {"UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "Email del usuario"}, "password": {"type": "string", "format": "password", "minLength": 6, "description": "Nueva contraseña (opcional)"}, "firstName": {"type": "string", "minLength": 1, "description": "Nombre del usuario"}, "lastName": {"type": "string", "minLength": 1, "description": "Apellido del usuario"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario"}, "enabled": {"type": "boolean", "description": "Estado habilitado del usuario"}}}, "UserProfileResponse": {"type": "object", "required": ["id", "email", "username", "firstName", "lastName", "role", "createdAt"], "properties": {"id": {"type": "integer", "format": "int64", "description": "ID único del usuario", "example": 1}, "email": {"type": "string", "format": "email", "description": "Email del usuario", "example": "<EMAIL>"}, "username": {"type": "string", "description": "Nombre de usuario único", "example": "john.doe"}, "firstName": {"type": "string", "description": "Nombre del usuario", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Apellido del usuario", "example": "<PERSON><PERSON>"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario en el sistema"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación del usuario", "example": "2024-01-01T12:00:00Z"}}}, "CreateUserRequest": {"type": "object", "required": ["email", "password", "firstName", "lastName"], "properties": {"email": {"type": "string", "format": "email", "description": "Email del usuario", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "minLength": 6, "description": "Contraseña del usuario"}, "firstName": {"type": "string", "minLength": 1, "description": "Nombre del usuario", "example": "<PERSON>"}, "lastName": {"type": "string", "minLength": 1, "description": "Apellido del usuario", "example": "<PERSON><PERSON>"}, "role": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Rol del usuario", "default": "USER"}, "enabled": {"type": "boolean", "description": "Estado habilitado del usuario", "default": true}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "Error de validación"}, "details": {"type": "object", "description": "Detalles adicionales del error", "properties": {}}}}, "MessageResponse": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "Mensaje de respuesta", "example": "Operación exitosa"}}}, "PagedUserResponse": {"type": "object", "required": ["content", "totalElements", "totalPages", "size", "number", "numberOfElements", "first", "last"], "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserProfileResponse"}, "description": "Lista de usuarios de la página actual"}, "totalElements": {"type": "integer", "format": "int64", "description": "Total de elementos"}, "totalPages": {"type": "integer", "description": "Total de páginas"}, "size": {"type": "integer", "description": "Tamaño de página"}, "number": {"type": "integer", "description": "Número de página actual"}, "numberOfElements": {"type": "integer", "description": "Número de elementos en la página actual"}, "first": {"type": "boolean", "description": "Indica si es la primera página"}, "last": {"type": "boolean", "description": "Indica si es la última página"}, "empty": {"type": "boolean", "description": "Indica si la página está vacía"}}}, "ActiveSessionInfo": {"type": "object", "required": ["sessionType", "lastActivity", "expiryDate"], "properties": {"sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión"}, "lastActivity": {"type": "string", "format": "date-time", "description": "Última actividad de la sesión"}, "expiryDate": {"type": "string", "format": "date-time", "description": "Fecha de expiración de la sesión"}}}, "PushTokenInfo": {"type": "object", "required": ["token", "deviceType", "deviceId", "createdAt"], "properties": {"token": {"type": "string", "description": "Token de notificaciones push", "example": "fcm-token-example-long-string"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo móvil"}, "deviceId": {"type": "string", "description": "Identificador único del dispositivo", "example": "device-uuid-12345"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación del token", "example": "2024-01-01T12:00:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de última actualización del token", "example": "2024-01-01T12:00:00Z"}}}, "AdminUserResponse": {"type": "object", "required": ["user", "activeSessions", "pushTokenInfo"], "properties": {"user": {"$ref": "#/components/schemas/UserProfileResponse"}, "activeSessions": {"type": "array", "items": {"$ref": "#/components/schemas/ActiveSessionInfo"}, "description": "Sesiones activas del usuario"}, "pushTokenInfo": {"allOf": [{"$ref": "#/components/schemas/PushTokenInfo"}], "description": "Información del token push (puede ser null)", "type": "null"}}}, "DetailedSessionInfo": {"type": "object", "required": ["username", "sessionType", "sessionCreatedAt", "sessionExpiresAt", "sessionId", "expired"], "properties": {"username": {"type": "string", "description": "Nombre de usuario", "example": "john.doe"}, "sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión"}, "clientIpAddress": {"type": "string", "description": "Dirección IP del cliente", "example": "*************"}, "userAgent": {"type": "string", "description": "User agent del cliente", "example": "Mozilla/5.0..."}, "sessionCreatedAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación de la sesión"}, "sessionExpiresAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de expiración de la sesión"}, "lastActivityAt": {"type": "string", "format": "date-time", "description": "Última actividad de la sesión"}, "sessionId": {"type": "string", "description": "ID único de la sesión", "example": "550e8400-e29b-41d4-a716-446655440000"}, "expired": {"type": "boolean", "description": "Indica si la sesión ha expirado"}, "timeToExpirationSeconds": {"type": "integer", "format": "int64", "description": "Tiempo restante hasta la expiración en segundos"}}}, "LoginRequest": {"type": "object", "required": ["identifier", "password", "sessionType"], "properties": {"identifier": {"type": "string", "description": "Email o nombre de usuario", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "minLength": 6, "description": "Contraseña del usuario", "example": "password123"}, "sessionType": {"type": "string", "enum": ["MOBILE", "WEB"], "description": "Tipo de sesión a crear"}, "pushToken": {"type": "string", "description": "Token para notificaciones push (solo sesiones móviles)", "example": "fcm-token-example"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo móvil"}, "deviceId": {"type": "string", "description": "Identificador único del dispositivo", "example": "device-123"}}}, "RefreshTokenRequest": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "description": "Token de renovación", "example": "eyJhbGciOiJIUzI1NiIs..."}}}, "ChangePasswordRequest": {"type": "object", "required": ["currentPassword", "newPassword", "confirmNewPassword"], "properties": {"currentPassword": {"type": "string", "format": "password", "description": "Contraseña actual del usuario"}, "newPassword": {"type": "string", "format": "password", "minLength": 6, "description": "Nueva contraseña"}, "confirmNewPassword": {"type": "string", "format": "password", "description": "Confirmación de la nueva contraseña"}}}, "AuthResponse": {"type": "object", "required": ["accessToken", "refreshToken", "tokenType", "expiresIn", "user"], "properties": {"accessToken": {"type": "string", "description": "Token JWT de acceso", "example": "eyJhbGciOiJIUzI1NiIs..."}, "refreshToken": {"type": "string", "description": "Token para renovar el access token", "example": "eyJhbGciOiJIUzI1NiIs..."}, "tokenType": {"type": "string", "default": "Bearer", "description": "Tipo de <PERSON>"}, "expiresIn": {"type": "integer", "format": "int64", "description": "Tiempo de expiración del access token en segundos", "example": 3600}, "user": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "FullNameRequest": {"type": "object", "required": ["usernames"], "properties": {"usernames": {"type": "array", "items": {"type": "string"}, "description": "Lista de usernames para obtener sus nombres completos", "minItems": 1, "maxItems": 100, "example": ["jdoe", "msmith", "agarcia"]}}}, "FullNameResponse": {"type": "object", "required": ["fullNames"], "properties": {"fullNames": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Mapeo de username a nombre completo (firstName + lastName)", "properties": {}, "example": {"jdoe": "<PERSON>", "msmith": "<PERSON>", "agarcia": "<PERSON>"}}}}, "BasicHealthResponse": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado general del sistema"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp de la verificación"}, "uptime": {"type": "string", "description": "Tiempo que el sistema ha estado funcionando", "example": "2h 30m 45s"}, "version": {"type": "string", "description": "Versión de la aplicación", "example": "1.0.0"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "DetailedHealthResponse": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado general del sistema"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp de la verificación"}, "uptime": {"type": "string", "description": "Tiempo que el sistema ha estado funcionando"}, "version": {"type": "string", "description": "Versión de la aplicación"}, "components": {"type": "object", "description": "Estado de componentes individuales", "properties": {"database": {"$ref": "#/components/schemas/DatabaseHealthComponent"}, "jwt": {"$ref": "#/components/schemas/JwtHealthComponent"}, "diskSpace": {"$ref": "#/components/schemas/DiskSpaceHealthComponent"}}}, "metrics": {"type": "object", "description": "Métricas del sistema", "properties": {"totalUsers": {"type": "integer", "description": "Total de usuarios registrados"}, "activeSessions": {"type": "integer", "description": "Sesiones activas actuales"}, "totalRequests": {"type": "integer", "description": "Total de requests procesados"}, "averageResponseTime": {"type": "string", "description": "Tiempo promedio de respuesta", "example": "120ms"}}}}}, "DatabaseHealthComponent": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado de la base de datos"}, "responseTime": {"type": "string", "description": "Tiempo de respuesta de la base de datos", "example": "15ms"}, "activeConnections": {"type": "integer", "description": "Conexiones activas actuales"}, "maxConnections": {"type": "integer", "description": "Máximo número de conexiones"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "JwtHealthComponent": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado del servicio JWT"}, "tokensIssued": {"type": "integer", "description": "Tokens emitidos en el período actual"}, "tokensValidated": {"type": "integer", "description": "Tokens validados en el período actual"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "DiskSpaceHealthComponent": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["UP", "DOWN"], "description": "Estado del espacio en disco"}, "free": {"type": "string", "description": "Espacio libre disponible", "example": "15.2 GB"}, "total": {"type": "string", "description": "Espacio total en disco", "example": "50 GB"}, "threshold": {"type": "string", "description": "Umbral de espacio mínimo", "example": "10%"}, "error": {"type": "string", "description": "Mensaje de error si el estado es DOWN"}}}, "ErrorHealthResponse": {"type": "object", "required": ["status", "error"], "properties": {"status": {"type": "string", "enum": ["DOWN"], "description": "Estado del sistema"}, "error": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "Health check service unavailable"}}}, "PasswordRecoveryCodeResponse": {"type": "object", "required": ["success", "data"], "properties": {"success": {"type": "boolean", "description": "Indica si la operación fue exitosa", "example": true}, "data": {"$ref": "#/components/schemas/PasswordRecoveryData"}}}, "PasswordRecoveryData": {"type": "object", "required": ["code", "email", "expiresAt", "createdAt"], "properties": {"code": {"type": "string", "description": "Código de recuperación de 8 caracteres alfanuméricos", "example": "A1B2C3D4"}, "email": {"type": "string", "format": "email", "description": "Email del usuario", "example": "<EMAIL>"}, "expiresAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de expiración del código", "example": "2025-09-24T15:00:00"}, "createdAt": {"type": "string", "format": "date-time", "description": "Fecha y hora de creación del código", "example": "2025-09-24T14:00:00"}}}, "PasswordRecoveryErrorResponse": {"type": "object", "required": ["success", "error"], "properties": {"success": {"type": "boolean", "description": "Indica si la operación fue exitosa", "example": false}, "error": {"$ref": "#/components/schemas/PasswordRecoveryErrorDetails"}}}, "PasswordRecoveryErrorDetails": {"type": "object", "required": ["code", "message", "timestamp"], "properties": {"code": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "enum": ["USER_NOT_FOUND", "USER_INACTIVE", "INVALID_CODE", "EXPIRED_CODE", "USED_CODE", "RATE_LIMIT_EXCEEDED"], "example": "INVALID_CODE"}, "message": {"type": "string", "description": "Mensaje descriptivo del error", "example": "El código proporcionado no es válido"}, "timestamp": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON> y hora del error", "example": "2025-09-24T14:00:00"}}}, "PasswordResetRequest": {"type": "object", "required": ["code", "newPassword"], "properties": {"code": {"type": "string", "minLength": 8, "maxLength": 8, "description": "Código de recuperación de 8 caracteres", "example": "A1B2C3D4"}, "newPassword": {"type": "string", "format": "password", "minLength": 6, "description": "Nueva contraseña del usuario", "example": "NuevaContraseña123!"}}}, "PasswordResetResponse": {"type": "object", "required": ["success", "message", "timestamp"], "properties": {"success": {"type": "boolean", "description": "Indica si la operación fue exitosa", "example": true}, "message": {"type": "string", "description": "Mensaje de confirmación", "example": "Contraseña actualizada exitosamente"}, "timestamp": {"type": "string", "format": "date-time", "description": "Fecha y hora de la operación", "example": "2025-09-24T14:00:00"}}}, "PushTokenRequest": {"type": "object", "required": ["pushToken", "deviceType", "deviceId"], "properties": {"pushToken": {"type": "string", "description": "Token de notificaciones push (FCM para Android, APNS para iOS)", "minLength": 1, "example": "fcm-token-example-long-string"}, "deviceType": {"type": "string", "enum": ["ANDROID", "IOS"], "description": "Tipo de dispositivo móvil"}, "deviceId": {"type": "string", "description": "Identificador único del dispositivo", "minLength": 1, "example": "device-uuid-12345"}}}}, "securitySchemes": {"bearer": {"type": "http", "scheme": "bearer"}}}, "servers": [], "security": [{"bearer": []}]}