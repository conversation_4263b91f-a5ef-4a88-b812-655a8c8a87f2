# CI/CD Setup - GitHub Actions

## Configuración de Secrets Requeridos

Para que el workflow funcione correctamente, configura los siguientes secrets en GitHub:

### Secrets del Repositorio
Ir a: **Settings** → **Secrets and variables** → **Actions** → **New repository secret**

| Secret Name | Descripción | Valor de Ejemplo |
|------------|-------------|------------------|
| `REGISTRY_USERNAME` | Usuario para registry.gedsys.dev | `deploy-user` |
| `REGISTRY_PASSWORD` | Token/contraseña del registry | `ghp_xxxxxxxxxxxxx` |

## Configuración del Environment

1. Crear environment "production" en **Settings** → **Environments**
2. Configurar protection rules si es necesario
3. Agregar reviewers requeridos para deployment

## Archivos Creados

### `.github/workflows/deploy.yml`
Workflow principal con las siguientes etapas:

- **build**: Compilación y tests con Maven
- **security-scan**: Análisis de vulnerabilidades con Trivy
- **docker**: Build y push de imagen Docker multi-arquitectura
- **deploy**: Deployment a producción (placeholder)

### `Dockerfile`
Imagen Docker multi-stage optimizada:
- **Builder stage**: Compilación con JDK 21
- **Runtime stage**: Ejecución con JRE 21 Alpine
- Usuario no-root para seguridad
- Health checks integrados
- Optimizaciones JVM para contenedores

### `.dockerignore`
Excluye archivos innecesarios del contexto Docker

## Estrategia de Tags

Las imágenes se etiquetan automáticamente:
- `latest`: Para push a main branch
- `main-{sha}`: Combinación de branch y commit SHA
- `{branch}`: Nombre del branch para PRs

## Mejores Prácticas Implementadas

### Seguridad
- Análisis estático con SpotBugs + FindSecBugs
- Escaneo de vulnerabilidades con Trivy (filesystem y container)
- Usuario no-root en contenedor
- Secrets gestionados por GitHub

### Performance
- Cache de dependencias Maven (.m2)
- Cache de layers Docker (GitHub Actions Cache)
- Build multi-arquitectura (AMD64/ARM64)
- Imagen Alpine para menor tamaño

### Calidad
- Tests automáticos
- Security scanning obligatorio
- Deployment solo en main branch
- Environment protection para producción

## Comandos Útiles

### Test local del Dockerfile:
```bash
docker build -t gedsys2-auth:local .
docker run -p 8080:8080 gedsys2-auth:local
```

### Verificar health check:
```bash
curl http://localhost:8080/actuator/health
```

## Estrategia de Rollback

En caso de problemas con deployment:

1. **Rollback rápido**: Usar tag anterior conocido
2. **Revert commit**: Revertir cambios en main
3. **Hotfix**: Branch de emergencia con fix específico

### Ejemplo de rollback manual:
```bash
# Obtener último tag funcional
docker pull registry.gedsys.dev/gedsys2/gedsys2-authentication:main-{previous-sha}

# Re-deploy imagen anterior
# (comandos específicos según infrastructure)
```

## Monitoring y Alertas

Configurar alertas para:
- Fallos en pipeline CI/CD
- Vulnerabilidades críticas detectadas
- Health checks fallidos en producción
- Uso excesivo de recursos en contenedores

## Próximos Pasos

1. Configurar secrets en repositorio
2. Customizar step de deployment según infrastructure
3. Configurar monitoring y alertas
4. Implementar tests de integración
5. Configurar notifications (Slack, email, etc.)