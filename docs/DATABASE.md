# Database Documentation

## Overview

The GEDSYS Authentication Service uses PostgreSQL 17 as its primary database with Flyway for schema migrations. The database is designed to support JWT-based authentication, user management, and push token functionality.

## Database Configuration

### Connection Settings
```properties
# Production
spring.datasource.url=**************************************************
spring.datasource.username=${DB_USERNAME:auth_user}
spring.datasource.password=${DB_PASSWORD:auth_password}

# Development (Docker Compose)
spring.datasource.url=***************************************************
```

### Connection Pool (HikariCP)
```properties
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000
```

## Schema Overview

### Tables
1. **users** - User accounts and profiles
2. **refresh_tokens** - JWT refresh token management
3. **push_tokens** - Mobile push notification tokens

### Relationships
- `refresh_tokens.user_id` → `users.id` (Many-to-One)
- `push_tokens.user_id` → `users.id` (One-to-One)

## Table Schemas

### Users Table (V1__Create_users_table.sql)

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    username VARCHAR(100) UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    role VARCHAR(20) NOT NULL DEFAULT 'USER',
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_enabled ON users(enabled);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Constraints
ALTER TABLE users ADD CONSTRAINT chk_users_role 
    CHECK (role IN ('USER', 'ADMIN'));
ALTER TABLE users ADD CONSTRAINT chk_users_status 
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED'));
```

**Columns:**
- `id` - Primary key, auto-incrementing
- `email` - Unique email address, used for authentication
- `password` - BCrypt hashed password
- `first_name` - User's first name (optional)
- `last_name` - User's last name (optional)
- `username` - Unique username, auto-generated from first and last name
- `status` - User status (ACTIVE, INACTIVE, DELETED) for logical deletion
- `role` - User role (USER, ADMIN)
- `enabled` - Legacy account status flag (maintained for backward compatibility)
- `created_at` - Account creation timestamp
- `updated_at` - Last modification timestamp

**Indexes:**
- Primary key on `id`
- Unique index on `email`
- Unique index on `username`
- Performance indexes on `status`, `role`, `enabled`, `created_at`

### Refresh Tokens Table (V2__Create_refresh_tokens_table.sql)

```sql
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(500) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    session_type VARCHAR(20) NOT NULL,
    expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX idx_refresh_tokens_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_tokens_session_type ON refresh_tokens(session_type);

-- Unique constraint for user + session type
CREATE UNIQUE INDEX idx_refresh_tokens_user_session 
    ON refresh_tokens(user_id, session_type) 
    WHERE used = false AND expiry_date > CURRENT_TIMESTAMP;

-- Constraints
ALTER TABLE refresh_tokens ADD CONSTRAINT chk_refresh_tokens_session_type 
    CHECK (session_type IN ('MOBILE', 'WEB'));
```

**Columns:**
- `id` - Primary key, auto-incrementing
- `token` - Unique JWT refresh token string
- `user_id` - Foreign key to users table
- `session_type` - Session type (MOBILE, WEB)
- `expiry_date` - Token expiration timestamp
- `used` - Single-use flag
- `created_at` - Token creation timestamp

**Key Features:**
- **Single-use policy**: Tokens marked as used after refresh
- **Session type awareness**: Different tokens for mobile/web
- **Automatic cleanup**: Cascade delete when user is deleted
- **Unique constraint**: One active token per user per session type

### Push Tokens Table (V3__Create_push_tokens_table.sql)

```sql
CREATE TABLE push_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    push_token VARCHAR(500) NOT NULL,
    device_type VARCHAR(20) NOT NULL,
    device_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes
CREATE UNIQUE INDEX idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX idx_push_tokens_device_type ON push_tokens(device_type);
CREATE INDEX idx_push_tokens_last_used ON push_tokens(last_used_at);

-- Constraints
ALTER TABLE push_tokens ADD CONSTRAINT chk_push_tokens_device_type 
    CHECK (device_type IN ('IOS', 'ANDROID'));
```

**Columns:**
- `id` - Primary key, auto-incrementing
- `user_id` - Foreign key to users table (unique)
- `push_token` - FCM/APNS push notification token
- `device_type` - Device platform (IOS, ANDROID)
- `device_id` - Unique device identifier (optional)
- `created_at` - Token registration timestamp
- `last_used_at` - Last activity timestamp

**Key Features:**
- **One-to-one relationship**: One push token per user
- **Device awareness**: Supports iOS and Android platforms
- **Activity tracking**: Last used timestamp for monitoring
- **Automatic cleanup**: Cascade delete when user is deleted

## Migration Strategy

### Flyway Configuration
```properties
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true
```

### Migration Files
- **V1__Create_users_table.sql** - Initial user schema
- **V2__Create_refresh_tokens_table.sql** - Refresh token management
- **V3__Create_push_tokens_table.sql** - Push notification support
- **V4__fix_unique_constraint_for_session_policy.sql** - Session policy constraints
- **V5__Insert_default_admin_user.sql** - Default administrator account
- **V6__add_session_id_to_refresh_tokens.sql** - Session ID tracking
- **V7__Add_username_and_status_fields.sql** - Username generation and user status management

### Migration Best Practices
1. **Versioned migrations**: Sequential version numbers
2. **Descriptive names**: Clear migration purposes
3. **Rollback scripts**: Consider rollback strategies
4. **Data migrations**: Separate data from schema changes
5. **Testing**: Validate migrations in development first

### Default Data Setup

#### Default Administrator Account (V5__Insert_default_admin_user.sql)
The system automatically creates a default administrator account for initial access:

```sql
INSERT INTO users (
    email,
    password,
    first_name,
    last_name,
    role,
    enabled,
    created_at,
    updated_at
) VALUES (
    '<EMAIL>',
    '$2a$10$RXY9gYyzn2qeW2AfB5eycez9wtXgJ0tJYMbf3mzrQ.cQaB8uMNTZu', -- BCrypt hash for 'admin123'
    'System',
    'Administrator',
    'ADMIN',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;
```

**Default Credentials:**
- **Email**: `<EMAIL>`
- **Password**: `admin123` (BCrypt encoded)
- **Role**: `ADMIN`
- **Status**: Enabled

**Security Considerations:**
- The migration uses `ON CONFLICT (email) DO NOTHING` to prevent duplicate entries
- Password is properly BCrypt hashed using the same algorithm as the application
- **Critical**: Change the default password immediately after first login in production
- Consider disabling or removing this account after creating proper admin users

### Username and Status Enhancement (V7__Add_username_and_status_fields.sql)
This migration adds username generation and user status management capabilities:

```sql
-- Add username field (initially nullable for existing users)
ALTER TABLE users ADD COLUMN username VARCHAR(100);

-- Add status field with default value for existing users
ALTER TABLE users ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' 
    CHECK (status IN ('ACTIVE', 'INACTIVE', 'DELETED'));

-- Migrate existing data: set status based on enabled field
UPDATE users SET status = 'ACTIVE' WHERE enabled = true;
UPDATE users SET status = 'INACTIVE' WHERE enabled = false;

-- Create indexes for performance optimization
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- Add comments for documentation
COMMENT ON COLUMN users.username IS 'Unique username generated from first and last name';
COMMENT ON COLUMN users.status IS 'User status: ACTIVE, INACTIVE, or DELETED for logical deletion';
```

**Key Features:**
- **Username Generation**: Automatic unique username creation from first and last names
- **Logical Deletion**: User status management (ACTIVE, INACTIVE, DELETED)
- **Data Migration**: Existing users' status is set based on their enabled flag
- **Performance Optimization**: New indexes for username and status queries
- **Backward Compatibility**: Enabled field is maintained alongside status

**Username Generation Algorithm:**
1. Take first initial of firstName + full lastName (lowercase)
2. Remove diacritics, spaces, and special characters
3. Check uniqueness against all users (including deleted ones)
4. Add incremental numbers if duplicates exist (e.g., "jdoe", "jdoe2", "jdoe3")

**Status Migration Logic:**
- `enabled = true` → `status = 'ACTIVE'`
- `enabled = false` → `status = 'INACTIVE'`
- New users default to `status = 'ACTIVE'`

## Performance Considerations

### Indexing Strategy
- **Primary keys**: Automatic B-tree indexes
- **Foreign keys**: Indexes on all foreign key columns
- **Query optimization**: Indexes on frequently queried columns
- **Composite indexes**: Multi-column indexes for complex queries

### Query Optimization
- **Pagination**: Efficient LIMIT/OFFSET with proper indexes
- **Filtering**: Indexes on filterable columns (email, role, enabled)
- **Sorting**: Indexes on sortable columns (created_at, email)
- **Joins**: Proper foreign key indexes for join performance

### Connection Pooling
- **HikariCP**: High-performance connection pool
- **Pool sizing**: Configured based on expected load
- **Connection lifecycle**: Proper timeout and validation settings

## Data Types and Constraints

### String Fields
- **VARCHAR(255)**: Standard string fields (email, names)
- **VARCHAR(500)**: Long strings (tokens)
- **VARCHAR(20)**: Enum-like fields (role, session_type)

### Numeric Fields
- **BIGSERIAL**: Auto-incrementing primary keys
- **BIGINT**: Foreign key references

### Date/Time Fields
- **TIMESTAMP WITH TIME ZONE**: All timestamps include timezone
- **DEFAULT CURRENT_TIMESTAMP**: Automatic timestamp creation

### Boolean Fields
- **BOOLEAN**: Simple true/false flags
- **DEFAULT values**: Sensible defaults for optional fields

## Security Considerations

### Data Protection
- **Password hashing**: BCrypt with salt
- **Token storage**: Secure token generation and storage
- **Cascade deletes**: Proper cleanup of related data

### Access Control
- **Database users**: Separate users for application and admin access
- **Connection encryption**: SSL/TLS for database connections
- **Audit logging**: Consider audit trails for sensitive operations

### Backup and Recovery
- **Regular backups**: Automated backup strategy
- **Point-in-time recovery**: Transaction log backups
- **Disaster recovery**: Cross-region backup replication

## Development and Testing

### Local Development
```bash
# Start PostgreSQL with Docker Compose
docker-compose up -d

# Run Flyway migrations
./mvnw flyway:migrate

# Check migration status
./mvnw flyway:info
```

### Test Database
- **H2 in-memory**: Unit tests
- **Testcontainers**: Integration tests with real PostgreSQL
- **Test data**: Controlled test data setup and cleanup

### Password Hash Generation
For creating BCrypt password hashes for migrations or testing, use the utility class:

```java
// Located at: src/test/java/co/com/gedsys/authentication/util/PasswordHashGenerator.java
@Test
public void generateAdminPasswordHash() {
    BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
    String password = "admin123";
    String hash = encoder.encode(password);
    
    System.out.println("Password: " + password);
    System.out.println("BCrypt Hash: " + hash);
    
    // Verify the hash works
    boolean matches = encoder.matches(password, hash);
    System.out.println("Hash verification: " + matches);
}
```

Run this test to generate new password hashes for migrations or test data.

### Database Tools
- **pgAdmin**: Web-based PostgreSQL administration
- **DBeaver**: Universal database tool
- **psql**: Command-line PostgreSQL client

## Monitoring and Maintenance

### Health Checks
```sql
-- Check database connectivity
SELECT 1;

-- Check table sizes
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public';
```

### Performance Monitoring
```sql
-- Check slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Check index usage
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public';
```

### Maintenance Tasks
- **VACUUM**: Regular table maintenance
- **ANALYZE**: Update table statistics
- **REINDEX**: Rebuild indexes when needed
- **Cleanup**: Remove expired tokens and sessions

## Future Considerations

### Scalability
- **Read replicas**: Scale read operations
- **Partitioning**: Table partitioning for large datasets
- **Sharding**: Horizontal scaling strategies

### Additional Features
- **Audit logging**: Track all database changes
- **Soft deletes**: Logical deletion with recovery options
- **Data archiving**: Archive old data for compliance
- **Multi-tenancy**: Support for multiple organizations